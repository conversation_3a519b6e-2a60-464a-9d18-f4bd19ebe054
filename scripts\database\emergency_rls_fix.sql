-- Emergency RLS Policy Fix for user_interest_tags
-- Apply this directly in Supabase SQL Editor

-- Step 1: Drop existing restrictive policies
DROP POLICY IF EXISTS "Users can view interest tags they created or received" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can create interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can update their own interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can delete their own interest tags" ON public.user_interest_tags;

-- Step 2: Create permissive policies for authenticated users
CREATE POLICY "Allow authenticated users to read all tags" ON public.user_interest_tags
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow authenticated users to create tags" ON public.user_interest_tags
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL 
    AND auth.uid() = tagger_id
  );

CREATE POLICY "Allow users to update their own tags" ON public.user_interest_tags
  FOR UPDATE USING (auth.uid() = tagger_id);

CREATE POLICY "Allow users to delete their own tags" ON public.user_interest_tags
  FOR DELETE USING (auth.uid() = tagger_id);

-- Step 3: Verify the table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_interest_tags' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Step 4: Test query
SELECT 'RLS policies updated for user_interest_tags' as status;
