import React, { useState } from 'react';
import { Database, AlertTriangle, <PERSON><PERSON>, CheckCircle, ExternalLink } from 'lucide-react';

interface ConversationBoundariesSetupProps {
  onClose: () => void;
}

const ConversationBoundariesSetup: React.FC<ConversationBoundariesSetupProps> = ({ onClose }) => {
  const [copied, setCopied] = useState(false);

  const sqlScript = `-- ====================================================================
-- Conversation Boundaries Management System
-- ====================================================================

-- Create conversation_boundaries table for detailed boundary settings
CREATE TABLE IF NOT EXISTS conversation_boundaries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  
  -- Communication preferences
  allow_messages_from TEXT DEFAULT 'everyone' CHECK (allow_messages_from IN ('everyone', 'tagged_only', 'mutual_interest', 'none')),
  require_introduction BOOLEAN DEFAULT false,
  auto_decline_explicit BOOLEAN DEFAULT true,
  
  -- Response time expectations
  response_time_expectation TEXT DEFAULT 'flexible' CHECK (response_time_expectation IN ('immediate', 'within_hours', 'within_day', 'flexible')),
  availability_window_start TIME,
  availability_window_end TIME,
  availability_timezone TEXT DEFAULT 'UTC',
  
  -- Content filtering
  block_explicit_content BOOLEAN DEFAULT true,
  block_personal_questions_early BOOLEAN DEFAULT false,
  block_meeting_requests_early BOOLEAN DEFAULT false,
  block_contact_sharing_early BOOLEAN DEFAULT true,
  
  -- Custom boundaries (JSON array of strings)
  custom_boundaries JSONB DEFAULT '[]'::jsonb,
  
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create blocked_users table
CREATE TABLE IF NOT EXISTS blocked_users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  blocker_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  blocked_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  reason TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  
  -- Ensure a user can't block the same person twice
  UNIQUE(blocker_id, blocked_id)
);

-- Create conversation_reports table for reporting inappropriate behavior
CREATE TABLE IF NOT EXISTS conversation_reports (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  reporter_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  reported_user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  conversation_id uuid, -- Reference to conversation if applicable
  report_type TEXT NOT NULL CHECK (report_type IN ('harassment', 'inappropriate_content', 'spam', 'fake_profile', 'other')),
  description TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE conversation_boundaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE blocked_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_reports ENABLE ROW LEVEL SECURITY;

-- RLS Policies for conversation_boundaries
CREATE POLICY "Users can view their own boundaries" ON conversation_boundaries
  FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert their own boundaries" ON conversation_boundaries
  FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update their own boundaries" ON conversation_boundaries
  FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can delete their own boundaries" ON conversation_boundaries
  FOR DELETE USING (auth.uid()::text = user_id::text);

-- RLS Policies for blocked_users
CREATE POLICY "Users can view their blocked users" ON blocked_users
  FOR SELECT USING (auth.uid()::text = blocker_id::text);

CREATE POLICY "Users can block other users" ON blocked_users
  FOR INSERT WITH CHECK (auth.uid()::text = blocker_id::text);

CREATE POLICY "Users can unblock users" ON blocked_users
  FOR DELETE USING (auth.uid()::text = blocker_id::text);

-- RLS Policies for conversation_reports
CREATE POLICY "Users can view their own reports" ON conversation_reports
  FOR SELECT USING (auth.uid()::text = reporter_id::text);

CREATE POLICY "Users can create reports" ON conversation_reports
  FOR INSERT WITH CHECK (auth.uid()::text = reporter_id::text);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversation_boundaries_user_id ON conversation_boundaries(user_id);
CREATE INDEX IF NOT EXISTS idx_blocked_users_blocker_id ON blocked_users(blocker_id);
CREATE INDEX IF NOT EXISTS idx_blocked_users_blocked_id ON blocked_users(blocked_id);
CREATE INDEX IF NOT EXISTS idx_conversation_reports_reporter_id ON conversation_reports(reporter_id);
CREATE INDEX IF NOT EXISTS idx_conversation_reports_reported_user_id ON conversation_reports(reported_user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_conversation_boundaries_updated_at 
  BEFORE UPDATE ON conversation_boundaries 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversation_reports_updated_at 
  BEFORE UPDATE ON conversation_reports 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default boundaries for existing users
INSERT INTO conversation_boundaries (user_id)
SELECT id FROM profiles 
WHERE id NOT IN (SELECT user_id FROM conversation_boundaries)
ON CONFLICT DO NOTHING;`;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(sqlScript);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy SQL script:', err);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Database className="w-8 h-8 text-blue-500 mr-3" />
            <div>
              <h2 className="text-2xl font-bold text-gray-800">Setup Required</h2>
              <p className="text-gray-600 mt-1">Conversation Boundaries feature needs to be set up</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-yellow-800 mb-2">Database Setup Required</h3>
                <p className="text-yellow-700 text-sm">
                  The conversation boundaries feature requires additional database tables that haven't been created yet. 
                  Please run the SQL script below in your Supabase dashboard to enable this feature.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Setup Instructions</h3>
              <ol className="list-decimal list-inside space-y-2 text-gray-700">
                <li>Copy the SQL script below</li>
                <li>Open your Supabase dashboard</li>
                <li>Navigate to the SQL Editor</li>
                <li>Paste and run the script</li>
                <li>Refresh this page to use the conversation boundaries feature</li>
              </ol>
            </div>

            <div>
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-gray-800">SQL Migration Script</h3>
                <div className="flex gap-2">
                  <button
                    onClick={handleCopy}
                    className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                  >
                    {copied ? (
                      <>
                        <CheckCircle className="w-4 h-4" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4" />
                        Copy Script
                      </>
                    )}
                  </button>
                  <a
                    href="https://supabase.com/dashboard"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                  >
                    <ExternalLink className="w-4 h-4" />
                    Open Supabase
                  </a>
                </div>
              </div>
              
              <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre className="text-green-400 text-sm whitespace-pre-wrap font-mono">
                  {sqlScript}
                </pre>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">What this script creates:</h4>
              <ul className="text-blue-700 text-sm space-y-1">
                <li>• <strong>conversation_boundaries</strong> - User communication preferences</li>
                <li>• <strong>blocked_users</strong> - User blocking relationships</li>
                <li>• <strong>conversation_reports</strong> - User reporting system</li>
                <li>• Row Level Security (RLS) policies for data protection</li>
                <li>• Indexes for optimal performance</li>
                <li>• Default settings for existing users</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConversationBoundariesSetup;
