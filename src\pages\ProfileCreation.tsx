import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowRight, CheckCircle, ArrowLeft } from 'lucide-react';
import { useProfile } from '../context/ProfileContext';
import { Profile, PersonalityTrait, Interest, RelationshipPreference } from '../types';

const ProfileCreation: React.FC = () => {
  const navigate = useNavigate();
  const { createProfile } = useProfile();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    age: 25,
    gender: '',
    location: '',
    bio: '',
    personalityTraits: [] as PersonalityTrait[],
    communicationStyle: [] as string[],
    interests: [] as Interest[],
    relationshipPreferences: [] as RelationshipPreference[],
    boundaries: [] as string[],
    values: [] as string[],
    preferredConversationTopics: [] as string[]
  });

  const totalSteps = 5;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handlePersonalityTraitToggle = (trait: PersonalityTrait) => {
    setFormData(prev => {
      const exists = prev.personalityTraits.some(t => t.id === trait.id);
      return {
        ...prev,
        personalityTraits: exists
          ? prev.personalityTraits.filter(t => t.id !== trait.id)
          : [...prev.personalityTraits, trait]
      };
    });
  };

  const handleValueToggle = (value: string) => {
    setFormData(prev => {
      const exists = prev.values.includes(value);
      return {
        ...prev,
        values: exists
          ? prev.values.filter(v => v !== value)
          : [...prev.values, value]
      };
    });
  };

  const handlePreferredConversationTopicToggle = (topic: string) => {
    setFormData(prev => {
      const exists = prev.preferredConversationTopics.includes(topic);
      return {
        ...prev,
        preferredConversationTopics: exists
          ? prev.preferredConversationTopics.filter(t => t !== topic)
          : [...prev.preferredConversationTopics, topic]
      };
    });
  };

  const handleInterestToggle = (interest: Interest) => {
    setFormData(prev => {
      const exists = prev.interests.some(i => i.id === interest.id);
      return {
        ...prev,
        interests: exists
          ? prev.interests.filter(i => i.id !== interest.id)
          : [...prev.interests, interest]
      };
    });
  };

  const handleCommunicationStyleToggle = (style: string) => {
    setFormData(prev => {
      const exists = prev.communicationStyle.includes(style);
      return {
        ...prev,
        communicationStyle: exists
          ? prev.communicationStyle.filter(s => s !== style)
          : [...prev.communicationStyle, style]
      };
    });
  };

  const handleRelationshipPreferenceToggle = (preference: RelationshipPreference & { description?: string }) => {
    // Only keep known fields for state
    const { id, type, importance } = preference;
    setFormData(prev => {
      const exists = prev.relationshipPreferences.some(p => p.id === id);
      const cleanPref = { id, type, importance };
      return {
        ...prev,
        relationshipPreferences: exists
          ? prev.relationshipPreferences.filter(p => p.id !== id)
          : [...prev.relationshipPreferences, cleanPref]
      };
    });
  };

  const handleBoundaryToggle = (boundary: string) => {
    setFormData(prev => {
      const exists = prev.boundaries.includes(boundary);
      return {
        ...prev,
        boundaries: exists
          ? prev.boundaries.filter(b => b !== boundary)
          : [...prev.boundaries, boundary]
      };
    });
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
    } else {
      completeProfile();
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const completeProfile = () => {
    const now = new Date().toISOString();
    const newProfile: Profile = {
      id: `profile-${Date.now()}`,
      name: formData.name,
      bio: formData.bio,
      interests: formData.interests,
      values: formData.values,
      preferredConversationTopics: formData.preferredConversationTopics,
      twin_context: {
        learned_preferences: [],
        recent_topics_discussed: [],
        communication_style_notes: [],
        key_facts_mentioned_by_user: [],
        sentiment_history: []
      },
      allow_persona_learning_from_chats: true,
      relationshipPreferences: formData.relationshipPreferences.map(({ id, type, importance }) => ({ id, type, importance })),
      createdAt: now,
      updatedAt: now
    };
    
    createProfile(newProfile);
    navigate('/dashboard');
  };

  // Sample data
  const personalityTraits: PersonalityTrait[] = [
    { id: 'trait1', name: 'Outgoing', value: 0 },
    { id: 'trait2', name: 'Thoughtful', value: 0 },
    { id: 'trait3', name: 'Creative', value: 0 },
    { id: 'trait4', name: 'Analytical', value: 0 },
    { id: 'trait5', name: 'Adventurous', value: 0 },
    { id: 'trait6', name: 'Caring', value: 0 },
    { id: 'trait7', name: 'Reliable', value: 0 },
    { id: 'trait8', name: 'Ambitious', value: 0 }
  ];

  const interests: Interest[] = [
    { id: 'int1', name: 'Reading', category: 'Hobbies' },
    { id: 'int2', name: 'Hiking', category: 'Outdoor' },
    { id: 'int3', name: 'Cooking', category: 'Food' },
    { id: 'int4', name: 'Photography', category: 'Arts' },
    { id: 'int5', name: 'Travel', category: 'Adventure' },
    { id: 'int6', name: 'Movies', category: 'Entertainment' },
    { id: 'int7', name: 'Music', category: 'Arts' },
    { id: 'int8', name: 'Technology', category: 'Professional' },
    { id: 'int9', name: 'Sports', category: 'Physical' },
    { id: 'int10', name: 'Art', category: 'Creative' }
  ];

  const communicationStyles = [
    'Direct', 'Thoughtful', 'Humorous', 'Intellectual', 'Casual', 'Flirty', 'Supportive', 'Curious'
  ];

  const relationshipPreferences: RelationshipPreference[] = [
    { id: 'rel1', type: 'friendship', importance: 7 },
    { id: 'rel2', type: 'dating', importance: 9 },
    { id: 'rel3', type: 'long-term', importance: 8 },
    { id: 'rel4', type: 'casual', importance: 5 }
  ];

  const boundaries = [
    'Personal questions too early',
    'Religious discussions',
    'Political debates',
    'Explicit content without consent',
    'Pressure to meet in person',
    'Sharing contact info too early',
    'Disrespectful language'
  ];

  const values = ['Honesty', 'Kindness', 'Ambition', 'Creativity', 'Loyalty', 'Open-mindedness', 'Humor'];
  const preferredConversationTopics = ['Deep Conversations', 'Current Events', 'Technology', 'Arts & Culture', 'Travel & Adventure', 'Food & Cooking', 'Personal Growth'];

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="animate-fadeIn">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Basic Information</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Your name"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="age" className="block text-sm font-medium text-gray-700 mb-1">Age</label>
                <input
                  type="number"
                  id="age"
                  name="age"
                  value={formData.age}
                  onChange={handleChange}
                  min="18"
                  max="100"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                <select
                  id="gender"
                  name="gender"
                  value={formData.gender}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  required
                >
                  <option value="">Select gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="non-binary">Non-binary</option>
                  <option value="other">Other</option>
                  <option value="prefer-not-to-say">Prefer not to say</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  value={formData.location || ''}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  placeholder="City, Country"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                <textarea
                  id="bio"
                  name="bio"
                  value={formData.bio}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Tell us about yourself..."
                  rows={4}
                  required
                />
              </div>
            </div>
          </div>
        );
      
      case 2:
        return (
          <div className="animate-fadeIn">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Personality & Communication</h2>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Personality Traits</label>
              <p className="text-sm text-gray-500 mb-3">Select traits that describe you (choose at least 3)</p>
              
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {personalityTraits.map(trait => (
                  <button
                    key={trait.id}
                    type="button"
                    onClick={() => handlePersonalityTraitToggle(trait)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium text-left transition duration-200 flex items-center justify-between ${
                      formData.personalityTraits.some(t => t.id === trait.id)
                        ? 'bg-purple-100 text-purple-800 border border-purple-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
                    }`}
                  >
                    <span>{trait.name}</span>
                    {formData.personalityTraits.some(t => t.id === trait.id) && (
                      <CheckCircle className="h-4 w-4 text-purple-600 ml-2" />
                    )}
                  </button>
                ))}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Communication Style</label>
              <p className="text-sm text-gray-500 mb-3">How do you typically communicate? (select all that apply)</p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {communicationStyles.map(style => (
                  <button
                    key={style}
                    type="button"
                    onClick={() => handleCommunicationStyleToggle(style)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium text-center transition duration-200 ${
                      formData.communicationStyle.includes(style)
                        ? 'bg-indigo-100 text-indigo-800 border border-indigo-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
                    }`}
                  >
                    {style}
                  </button>
                ))}
              </div>
            </div>
          </div>
        );
        
      case 3: // New Step 3: Values & Topics
        return (
          <div className="animate-fadeIn">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Values & Topics</h2>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Core Values</label>
              <p className="text-sm text-gray-500 mb-3">Select values that are important to you (choose at least 1)</p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {values.map(value => (
                  <button
                    key={value}
                    type="button"
                    onClick={() => handleValueToggle(value)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium text-left transition duration-200 flex items-center justify-between ${
                      formData.values.includes(value)
                        ? 'bg-green-100 text-green-800 border border-green-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
                    }`}
                  >
                    <span>{value}</span>
                    {formData.values.includes(value) && (
                      <CheckCircle className="h-4 w-4 text-green-600 ml-2" />
                    )}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Preferred Conversation Topics</label>
              <p className="text-sm text-gray-500 mb-3">What do you enjoy talking about? (select at least 1)</p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {preferredConversationTopics.map(topic => (
                  <button
                    key={topic}
                    type="button"
                    onClick={() => handlePreferredConversationTopicToggle(topic)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium text-left transition duration-200 flex items-center justify-between ${
                      formData.preferredConversationTopics.includes(topic)
                        ? 'bg-blue-100 text-blue-800 border border-blue-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
                    }`}
                  >
                    <span>{topic}</span>
                    {formData.preferredConversationTopics.includes(topic) && (
                      <CheckCircle className="h-4 w-4 text-blue-600 ml-2" />
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        );

      case 4: // Previously Step 3
        return (
          <div className="animate-fadeIn">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Interests & Preferences</h2>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Interests & Hobbies</label>
              <p className="text-sm text-gray-500 mb-3">Select things you enjoy (choose at least 3)</p>
              
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                {interests.map(interest => (
                  <button
                    key={interest.id}
                    type="button"
                    onClick={() => handleInterestToggle(interest)}
                    className={`px-3 py-2 rounded-lg text-sm text-center transition duration-200 ${
                      formData.interests.some(i => i.id === interest.id)
                        ? 'bg-purple-100 text-purple-800 border border-purple-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
                    }`}
                  >
                    {interest.name}
                  </button>
                ))}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Relationship Preferences</label>
              <p className="text-sm text-gray-500 mb-3">What are you looking for? (select all that apply)</p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {relationshipPreferences.map(preference => (
                  <button
                    key={preference.id}
                    type="button"
                    onClick={() => handleRelationshipPreferenceToggle(preference)}
                    className={`px-4 py-3 rounded-lg text-left transition duration-200 ${
                      formData.relationshipPreferences.some(p => p.id === preference.id)
                        ? 'bg-pink-100 text-pink-800 border border-pink-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium capitalize">
                        {preference.type.replace('-', ' ')}
                      </span>
                      {formData.relationshipPreferences.some(p => p.id === preference.id) && (
                        <CheckCircle className="h-5 w-5 text-pink-600" />
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        );
        
      case 5: // Previously Step 4
        return (
          <div className="animate-fadeIn">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Boundaries & Safety</h2>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Conversation Boundaries</label>
              <p className="text-sm text-gray-500 mb-3">Select topics or behaviors you prefer to avoid</p>
              
              <div className="space-y-2">
                {boundaries.map(boundary => (
                  <button
                    key={boundary}
                    type="button"
                    onClick={() => handleBoundaryToggle(boundary)}
                    className={`w-full px-4 py-3 rounded-lg text-left transition duration-200 flex justify-between items-center ${
                      formData.boundaries.includes(boundary)
                        ? 'bg-red-50 text-red-800 border border-red-100'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
                    }`}
                  >
                    <span>{boundary}</span>
                    {formData.boundaries.includes(boundary) && (
                      <CheckCircle className="h-5 w-5 text-red-600" />
                    )}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="bg-purple-50 border border-purple-100 rounded-lg p-4 mt-8">
              <h3 className="font-medium text-purple-800 mb-1">Digital Twin Creation</h3>
              <p className="text-sm text-purple-700">
                Based on your profile, we'll match you with digital twins that align with your personality, 
                interests, and relationship goals.
              </p>
            </div>
          </div>
        );
        
      default:
        return null;
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return (
          formData.name.trim() !== '' && 
          formData.age >= 18 && 
          formData.gender !== '' && 
          formData.bio.trim() !== ''
        );
      case 2:
        return (
          formData.personalityTraits.length >= 3 && 
          formData.communicationStyle.length >= 1
        );
      case 3: // New Step 3: Values & Topics
        return (
          formData.values.length >= 1 &&
          formData.preferredConversationTopics.length >= 1
        );
      case 4: // Previously Step 3
        return (
          formData.interests.length >= 3 && 
          formData.relationshipPreferences.length >= 1
        );
      case 5: // Previously Step 4
        return true; // Boundaries are optional
      default:
        return false;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-md overflow-hidden">
        <div className="bg-gradient-to-r from-purple-600 to-pink-500 px-6 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-white text-xl font-bold">Create Your Profile</h1>
            <div className="text-white text-sm">
              Step {currentStep} of {totalSteps}
            </div>
          </div>
          
          {/* Progress bar */}
          <div className="w-full bg-white/20 rounded-full h-2 mt-3">
            <div 
              className="bg-white h-2 rounded-full" 
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            ></div>
          </div>
        </div>
        
        <form className="p-6">
          {renderStep()}
          
          <div className="mt-8 flex justify-between">
            <button
              type="button"
              onClick={prevStep}
              className={`flex items-center px-5 py-2 rounded-lg transition duration-200 ${
                currentStep === 1
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              disabled={currentStep === 1}
            >
              <ArrowLeft className="h-5 w-5 mr-1" />
              Back
            </button>
            
            <button
              type="button"
              onClick={nextStep}
              className={`flex items-center px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium rounded-lg shadow-sm transition duration-200 ${
                isStepValid()
                  ? 'hover:from-purple-700 hover:to-pink-600'
                  : 'opacity-70 cursor-not-allowed'
              }`}
              disabled={!isStepValid()}
            >
              {currentStep === totalSteps ? 'Complete Profile' : 'Next'}
              <ArrowRight className="h-5 w-5 ml-1" />
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProfileCreation;