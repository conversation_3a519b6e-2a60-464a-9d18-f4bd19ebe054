import React, { useState, useRef } from 'react';
import { Send, Paperclip, Smile, UserPlus } from 'lucide-react';
import { useConversation } from '../../context/ConversationContext';
import { useToast } from '../../hooks/useToast';

interface ChatControlsProps {
  conversationId: string;
  onSwitchUser?: () => void;
  disabled?: boolean;
  isTwinChat?: boolean;
}

const ChatControls: React.FC<ChatControlsProps> = ({ 
  conversationId,
  onSwitchUser,
  disabled = false,
  isTwinChat = true
}) => {
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { sendMessage } = useConversation();
  const { showError } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() || disabled || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await sendMessage(conversationId, message);
      setMessage('');
    } catch (error) {
      showError('Failed to send message. Please try again.');
      console.error('Error sending message:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    // TODO: Implement file upload
  };

  return (
    <div className="border-t border-gray-200 p-4 bg-white">
      <form onSubmit={handleSubmit} className="flex items-end space-x-2">
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileChange}
          accept="image/*,.pdf,.doc,.docx"
        />
        
        {onSwitchUser && (
          <button
            type="button"
            onClick={onSwitchUser}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition"
            title={isTwinChat ? "Switch to real user" : "Switch to digital twin"}
          >
            <UserPlus className="w-5 h-5" />
          </button>
        )}

        <button
          type="button"
          onClick={handleFileSelect}
          className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition"
          disabled={disabled}
        >
          <Paperclip className="w-5 h-5" />
        </button>

        <div className="flex-grow">
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder={`Type your message${isTwinChat ? ' to AI' : ''}...`}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
            rows={1}
            disabled={disabled}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
          />
        </div>

        <button
          type="button"
          className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition"
          disabled={disabled}
        >
          <Smile className="w-5 h-5" />
        </button>

        <button
          type="submit"
          className={`px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-lg flex items-center transition ${
            disabled || !message.trim()
              ? 'opacity-50 cursor-not-allowed'
              : 'hover:from-purple-700 hover:to-pink-600'
          }`}
          disabled={disabled || !message.trim() || isSubmitting}
        >
          {isSubmitting ? (
            <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent" />
          ) : (
            <>
              <Send className="w-5 h-5 mr-2" />
              Send
            </>
          )}
        </button>
      </form>
    </div>
  );
};

export default ChatControls;