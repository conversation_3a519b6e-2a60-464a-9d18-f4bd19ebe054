// Test script to verify profiles were created
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVnZ2NjYmtoeHBlbXd1dHNxc3BoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc4MDQ5MDgsImV4cCI6MjA1MzM4MDkwOH0.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testAfterSQLFix() {
  console.log('🧪 Testing user discovery after SQL fix...');
  
  try {
    // 1. Check total profiles
    console.log('\n1️⃣ Checking total profiles...');
    const { data: allProfiles, error: allError } = await supabase
      .from('profiles')
      .select('id, username, full_name, age, gender');
    
    if (allError) {
      console.log('❌ Error fetching profiles:', allError);
      return;
    }
    
    console.log(`✅ Total profiles found: ${allProfiles?.length || 0}`);
    if (allProfiles && allProfiles.length > 0) {
      allProfiles.forEach(profile => {
        console.log(`   • ${profile.full_name} (@${profile.username}) - ${profile.age}y, ${profile.gender}`);
      });
    }

    // 2. Test search for "rob"
    console.log('\n2️⃣ Testing search for "rob"...');
    const { data: searchResults, error: searchError } = await supabase
      .from('profiles')
      .select('id, username, full_name, age, gender')
      .or('full_name.ilike.%rob%,username.ilike.%rob%');
    
    if (searchError) {
      console.log('❌ Search error:', searchError);
    } else {
      console.log(`✅ Found ${searchResults?.length || 0} Roberto users:`);
      searchResults?.forEach(user => {
        console.log(`   • ${user.full_name} (@${user.username}) - ID: ${user.id.substring(0, 8)}...`);
      });
    }

    // 3. Test user discovery (Roberto 1 looking for others)
    const currentUserId = '1818c8d3-c9a1-4a53-9b01-fc51e45ce55d'; // <EMAIL>
    console.log('\n3️⃣ Testing discovery (Roberto 1 should find other users)...');
    const { data: browseResults, error: browseError } = await supabase
      .from('profiles')
      .select('id, username, full_name, age, gender')
      .neq('id', currentUserId)
      .order('created_at', { ascending: false });
    
    if (browseError) {
      console.log('❌ Browse error:', browseError);
    } else {
      console.log(`✅ Roberto 1 can discover ${browseResults?.length || 0} other users:`);
      browseResults?.forEach(user => {
        console.log(`   • ${user.full_name} (@${user.username}) - ${user.age}y, ${user.gender}`);
      });
    }

    // 4. Specific check: Can Roberto 1 find Roberto 2?
    const otherRobertoId = 'c6f6f365-6150-4bf2-96ee-39be2ec41094';
    const otherRoberto = browseResults?.find(user => user.id === otherRobertoId);
    
    console.log('\n4️⃣ Can Roberto 1 discover Roberto 2?');
    if (otherRoberto) {
      console.log('🎉 YES! Roberto 1 can find Roberto 2');
      console.log(`   Found: ${otherRoberto.full_name} (@${otherRoberto.username})`);
    } else {
      console.log('❌ NO! Roberto 2 not found in discovery results');
    }

    // 5. Test search functionality specifically
    console.log('\n5️⃣ Testing app search function (searching for "roberto")...');
    const { data: appSearchResults, error: appSearchError } = await supabase
      .from('profiles')
      .select('id, username, full_name, age, gender')
      .or('full_name.ilike.%roberto%,username.ilike.%roberto%');
    
    if (appSearchError) {
      console.log('❌ App search error:', appSearchError);
    } else {
      console.log(`✅ App search found ${appSearchResults?.length || 0} results for "roberto":`);
      appSearchResults?.forEach(user => {
        console.log(`   • ${user.full_name} (@${user.username})`);
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testAfterSQLFix();
