import React, { useState, useEffect } from 'react';
import { PhoneCall, Check } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import {
  removeInterestTag,
  getUserTagForProfile,
  addInterestTag
} from '../../lib/userTagging';

interface WillingToContactButtonProps {
  profileId: string;
  profileName: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const WillingToContactButton: React.FC<WillingToContactButtonProps> = ({
  profileId,
  profileName,
  className = '',
  size = 'md'
}) => {
  const { user } = useAuth();
  const { showSuccess, showError, showLoading, dismiss } = useToast();
  const [isWillingToContact, setIsWillingToContact] = useState(false);
  const [loading, setLoading] = useState(false);

  // Size configurations
  const sizeConfig = {
    sm: {
      button: 'px-3 py-1.5 text-xs',
      icon: 'w-3 h-3'
    },
    md: {
      button: 'px-4 py-2 text-sm',
      icon: 'w-4 h-4'
    },
    lg: {
      button: 'px-6 py-3 text-base',
      icon: 'w-5 h-5'
    }
  };

  const config = sizeConfig[size];

  // Load existing status on mount
  useEffect(() => {
    if (user?.id && profileId) {
      loadWillingToContactStatus();
    }
  }, [user?.id, profileId]);

  const loadWillingToContactStatus = async () => {
    if (!user?.id) return;

    try {
      const tag = await getUserTagForProfile(profileId, user.id);
      setIsWillingToContact(tag?.tag_type === 'potential_match');
    } catch (err) {
      console.error('Failed to load willing to contact status:', err);
      setIsWillingToContact(false);
    }
  };

  const handleToggleWillingToContact = async () => {
    if (!user?.id || loading) return;

    setLoading(true);

    if (isWillingToContact) {
      // Remove the willing to contact tag
      const loadingToastId = showLoading('Removing willingness to contact...');
      
      try {
        const result = await removeInterestTag(profileId, user.id);
        
        if (result.success) {
          setIsWillingToContact(false);
          showSuccess('Removed willingness to contact');
        } else {
          showError(result.error || 'Failed to remove willingness to contact');
        }
      } catch (err) {
        showError('Failed to remove willingness to contact');
      } finally {
        dismiss(loadingToastId);
        setLoading(false);
      }
    } else {
      // Mark as willing to contact
      const loadingToastId = showLoading(`Marking willingness to contact ${profileName}...`);
      
      try {
        const result = await markWillingToContact(profileId, user.id);
        
        if (result.success) {
          setIsWillingToContact(true);
          showSuccess(`You've expressed willingness to contact ${profileName}! They'll receive a special notification.`);
        } else {
          showError(result.error || 'Failed to mark willingness to contact');
        }
      } catch (err) {
        showError('Failed to mark willingness to contact');
      } finally {
        dismiss(loadingToastId);
        setLoading(false);
      }
    }
  };

  if (!user?.id || profileId === user.id) {
    return null; // Don't show for own profile or when not logged in
  }

  return (
    <button
      onClick={handleToggleWillingToContact}
      disabled={loading}
      className={`
        ${config.button}
        inline-flex items-center justify-center
        font-medium rounded-lg transition-all duration-200
        focus:outline-none focus:ring-2 focus:ring-offset-2
        disabled:opacity-50 disabled:cursor-not-allowed
        ${isWillingToContact 
          ? 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500 border-2 border-green-600' 
          : 'bg-purple-600 hover:bg-purple-700 text-white focus:ring-purple-500 border-2 border-purple-600 hover:shadow-lg'
        }
        ${className}
      `}
      title={isWillingToContact 
        ? `You've expressed willingness to contact ${profileName}` 
        : `Express strong interest and willingness to contact ${profileName}`
      }
    >
      {isWillingToContact ? (
        <>
          <Check className={`${config.icon} mr-2`} />
          Willing to Contact
        </>
      ) : (
        <>
          <PhoneCall className={`${config.icon} mr-2`} />
          Willing to Contact
        </>
      )}
    </button>
  );
};

export default WillingToContactButton;
