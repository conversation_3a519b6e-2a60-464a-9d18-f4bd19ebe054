// Test script to verify tagging system functionality
// Run this after applying the SQL fixes

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testTaggingSystem() {
  console.log('🔍 Testing Tagging System...\n');

  try {
    // Test 1: Check if user_interest_tags table exists and constraint is updated
    console.log('1. Testing user_interest_tags table structure...');
    const { data: constraintInfo, error: constraintError } = await supabase
      .rpc('get_table_constraints', { table_name: 'user_interest_tags' });
    
    if (constraintError) {
      console.log('⚠️  Could not check constraints (this is expected if RPC doesn\'t exist)');
    } else {
      console.log('✅ Table constraints checked');
    }

    // Test 2: Try to insert a test tag (this will test the constraint)
    console.log('\n2. Testing tag type constraint...');
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('⚠️  No authenticated user - skipping insert tests');
      console.log('   To test properly, run this in the browser console when logged in');
    } else {
      // Test inserting a valid tag type
      const { error: insertError } = await supabase
        .from('user_interest_tags')
        .insert({
          tagger_id: user.id,
          tagged_user_id: user.id, // Self-tag for testing
          tag_type: 'willing_to_contact'
        });

      if (insertError) {
        console.log('❌ Insert failed:', insertError.message);
        
        // Check if it's a constraint error
        if (insertError.message.includes('violates check constraint')) {
          console.log('   💡 This indicates the database constraint hasn\'t been updated yet');
          console.log('   💡 Please run the SQL script: fix_tagging_database_issues.sql');
        } else if (insertError.message.includes('row-level security')) {
          console.log('   💡 This indicates RLS policy issues');
          console.log('   💡 Please run the SQL script: fix_tagging_database_issues.sql');
        }
      } else {
        console.log('✅ Tag insert successful');
        
        // Clean up test data
        await supabase
          .from('user_interest_tags')
          .delete()
          .eq('tagger_id', user.id)
          .eq('tagged_user_id', user.id)
          .eq('tag_type', 'willing_to_contact');
      }
    }

    // Test 3: Check if we can read from the tables
    console.log('\n3. Testing table read access...');
    
    const { data: tagsData, error: tagsError } = await supabase
      .from('user_interest_tags')
      .select('*')
      .limit(1);

    if (tagsError) {
      console.log('❌ Cannot read from user_interest_tags:', tagsError.message);
    } else {
      console.log('✅ Can read from user_interest_tags');
    }

    const { data: statusData, error: statusError } = await supabase
      .from('contact_willingness_status')
      .select('*')
      .limit(1);

    if (statusError) {
      console.log('❌ Cannot read from contact_willingness_status:', statusError.message);
    } else {
      console.log('✅ Can read from contact_willingness_status');
    }

    const { data: notificationsData, error: notificationsError } = await supabase
      .from('user_notifications')
      .select('*')
      .limit(1);

    if (notificationsError) {
      console.log('❌ Cannot read from user_notifications:', notificationsError.message);
    } else {
      console.log('✅ Can read from user_notifications');
    }

    console.log('\n🏁 Test completed!');
    console.log('\nNext steps:');
    console.log('1. If there were constraint errors, run: fix_tagging_database_issues.sql in Supabase');
    console.log('2. Test the tagging functionality in the app');
    console.log('3. Check that notifications are created when tags are added');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// If running in Node.js
if (typeof window === 'undefined') {
  testTaggingSystem();
}

// If running in browser
if (typeof window !== 'undefined') {
  window.testTaggingSystem = testTaggingSystem;
  console.log('🔧 Test function loaded. Run: testTaggingSystem()');
}

export { testTaggingSystem };
