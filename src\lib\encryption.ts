import CryptoJS from 'crypto-js';
import { nanoid } from 'nanoid';

// Generate a new encryption key
export const generateEncryptionKey = () => {
  return nanoid(32);
};

// Encrypt message content
export const encryptMessage = (content: string, key: string) => {
  return CryptoJS.AES.encrypt(content, key).toString();
};

// Decrypt message content
export const decryptMessage = (encryptedContent: string, key: string) => {
  const bytes = CryptoJS.AES.decrypt(encryptedContent, key);
  return bytes.toString(CryptoJS.enc.Utf8);
};