import { supabase } from './supabase';
import { ConversationBoundaries, BlockedUser, ConversationReport, Profile } from '../types';
import { ErrorLogger } from './errorHandler';

// ====================================================================
// Conversation Boundaries Management
// ====================================================================

export async function getConversationBoundaries(userId: string): Promise<ConversationBoundaries | null> {
  try {
    const { data, error } = await supabase
      .from('conversation_boundaries')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No boundaries found, create default ones
        return await createDefaultBoundaries(userId);
      }
      ErrorLogger.logError(error, 'database');
      return null;
    }

    return {
      ...data,
      custom_boundaries: Array.isArray(data.custom_boundaries) ? data.custom_boundaries : []
    };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return null;
  }
}

export async function createDefaultBoundaries(userId: string): Promise<ConversationBoundaries | null> {
  try {
    const defaultBoundaries = {
      user_id: userId,
      allow_messages_from: 'everyone' as const,
      require_introduction: false,
      auto_decline_explicit: true,
      response_time_expectation: 'flexible' as const,
      availability_timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC',
      block_explicit_content: true,
      block_personal_questions_early: false,
      block_meeting_requests_early: false,
      block_contact_sharing_early: true,
      custom_boundaries: []
    };

    const { data, error } = await supabase
      .from('conversation_boundaries')
      .insert(defaultBoundaries)
      .select()
      .single();

    if (error) {
      ErrorLogger.logError(error, 'database');
      return null;
    }

    return {
      ...data,
      custom_boundaries: Array.isArray(data.custom_boundaries) ? data.custom_boundaries : []
    };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return null;
  }
}

export async function updateConversationBoundaries(
  userId: string, 
  boundaries: Partial<ConversationBoundaries>
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('conversation_boundaries')
      .update(boundaries)
      .eq('user_id', userId);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to update conversation boundaries' };
  }
}

// ====================================================================
// Blocked Users Management
// ====================================================================

export async function getBlockedUsers(userId: string): Promise<BlockedUser[]> {
  try {
    const { data, error } = await supabase
      .from('blocked_users')
      .select(`
        *,
        blocked_user:profiles!blocked_users_blocked_id_fkey(
          id,
          username,
          full_name,
          avatar_url
        )
      `)
      .eq('blocker_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      ErrorLogger.logError(error, 'database');
      return [];
    }

    return data.map(item => ({
      ...item,
      blocked_user: item.blocked_user ? {
        id: item.blocked_user.id,
        name: item.blocked_user.full_name || item.blocked_user.username,
        username: item.blocked_user.username,
        avatar_url: item.blocked_user.avatar_url,
        createdAt: new Date(),
        updatedAt: new Date()
      } as Profile : undefined
    }));
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return [];
  }
}

export async function blockUser(
  blockerId: string, 
  blockedId: string, 
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('blocked_users')
      .insert({
        blocker_id: blockerId,
        blocked_id: blockedId,
        reason
      });

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to block user' };
  }
}

export async function unblockUser(
  blockerId: string, 
  blockedId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('blocked_users')
      .delete()
      .eq('blocker_id', blockerId)
      .eq('blocked_id', blockedId);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to unblock user' };
  }
}

// ====================================================================
// Reporting System
// ====================================================================

export async function reportUser(
  reporterId: string,
  reportedUserId: string,
  reportType: ConversationReport['report_type'],
  description?: string,
  conversationId?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('conversation_reports')
      .insert({
        reporter_id: reporterId,
        reported_user_id: reportedUserId,
        conversation_id: conversationId,
        report_type: reportType,
        description
      });

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to submit report' };
  }
}

// ====================================================================
// Utility Functions
// ====================================================================

export async function canUserMessage(
  senderId: string,
  recipientId: string
): Promise<{ canMessage: boolean; reason?: string }> {
  try {
    // Check if sender is blocked by recipient
    const { data: blockedData } = await supabase
      .from('blocked_users')
      .select('id')
      .eq('blocker_id', recipientId)
      .eq('blocked_id', senderId)
      .single();

    if (blockedData) {
      return { canMessage: false, reason: 'You have been blocked by this user' };
    }

    // Get recipient's boundaries
    const boundaries = await getConversationBoundaries(recipientId);
    if (!boundaries) {
      return { canMessage: true }; // Default to allowing if no boundaries set
    }

    // Check message permissions based on boundaries
    switch (boundaries.allow_messages_from) {
      case 'none':
        return { canMessage: false, reason: 'This user is not accepting messages' };

      case 'tagged_only':
        // Check if sender has tagged the recipient
        const { data: tagData } = await supabase
          .from('user_interest_tags')
          .select('id')
          .eq('tagger_id', senderId)
          .eq('tagged_user_id', recipientId)
          .single();

        if (!tagData) {
          return { canMessage: false, reason: 'This user only accepts messages from users who have tagged them' };
        }
        break;

      case 'mutual_interest':
        // Check for mutual tagging
        const { data: mutualTags } = await supabase
          .from('user_interest_tags')
          .select('id')
          .or(`and(tagger_id.eq.${senderId},tagged_user_id.eq.${recipientId}),and(tagger_id.eq.${recipientId},tagged_user_id.eq.${senderId})`);

        if (!mutualTags || mutualTags.length < 2) {
          return { canMessage: false, reason: 'This user only accepts messages from mutual interests' };
        }
        break;

      case 'everyone':
      default:
        // Allow messages from everyone
        break;
    }

    return { canMessage: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { canMessage: true }; // Default to allowing on error
  }
}

// Helper function to check if content violates boundaries
export function checkContentViolation(
  content: string,
  boundaries: ConversationBoundaries
): { isViolation: boolean; reason?: string } {
  const lowerContent = content.toLowerCase();

  if (boundaries.block_explicit_content) {
    const explicitKeywords = ['sex', 'sexual', 'nude', 'naked', 'porn', 'explicit'];
    if (explicitKeywords.some(keyword => lowerContent.includes(keyword))) {
      return { isViolation: true, reason: 'Message contains explicit content' };
    }
  }

  if (boundaries.block_personal_questions_early) {
    const personalKeywords = ['address', 'phone', 'number', 'where do you live', 'personal info'];
    if (personalKeywords.some(keyword => lowerContent.includes(keyword))) {
      return { isViolation: true, reason: 'Personal questions are not allowed early in conversations' };
    }
  }

  if (boundaries.block_meeting_requests_early) {
    const meetingKeywords = ['meet up', 'meet in person', 'coffee', 'date', 'hang out'];
    if (meetingKeywords.some(keyword => lowerContent.includes(keyword))) {
      return { isViolation: true, reason: 'Meeting requests are not allowed early in conversations' };
    }
  }

  if (boundaries.block_contact_sharing_early) {
    const contactKeywords = ['instagram', 'facebook', 'snapchat', 'whatsapp', 'telegram', '@'];
    if (contactKeywords.some(keyword => lowerContent.includes(keyword))) {
      return { isViolation: true, reason: 'Contact sharing is not allowed early in conversations' };
    }
  }

  return { isViolation: false };
}
