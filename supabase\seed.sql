-- Seed data for testing authentication and profile discovery

-- Add test users to auth.users (for local development testing)
INSERT INTO auth.users (
  id, 
  aud, 
  role, 
  email, 
  encrypted_password, 
  email_confirmed_at, 
  created_at, 
  updated_at, 
  raw_app_meta_data, 
  raw_user_meta_data, 
  is_super_admin, 
  confirmation_token, 
  recovery_token, 
  email_change_token_new, 
  email_change
) VALUES 
  ('550e8400-e29b-41d4-a716-446655440001', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider": "google", "providers": ["google"]}', '{"full_name": "<PERSON>", "email": "<EMAIL>", "avatar_url": "https://images.unsplash.com/photo-1494790108755-2616b612b830"}', false, '', '', '', ''),
  ('550e8400-e29b-41d4-a716-446655440002', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider": "google", "providers": ["google"]}', '{"full_name": "Bob Smith", "email": "<EMAIL>", "avatar_url": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e"}', false, '', '', '', ''),
  ('550e8400-e29b-41d4-a716-446655440003', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider": "google", "providers": ["google"]}', '{"full_name": "Carol Davis", "email": "<EMAIL>", "avatar_url": "https://images.unsplash.com/photo-1438761681033-6461ffad8d80"}', false, '', '', '', ''),
  ('550e8400-e29b-41d4-a716-446655440004', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider": "google", "providers": ["google"]}', '{"full_name": "David Wilson", "email": "<EMAIL>", "avatar_url": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d"}', false, '', '', '', ''),
  ('550e8400-e29b-41d4-a716-446655440005', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider": "google", "providers": ["google"]}', '{"full_name": "Emma Garcia", "email": "<EMAIL>", "avatar_url": "https://images.unsplash.com/photo-**********-94ddf0286df2"}', false, '', '', '', ''),
  ('550e8400-e29b-41d4-a716-446655440006', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider": "google", "providers": ["google"]}', '{"full_name": "Test User", "email": "<EMAIL>", "avatar_url": "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde"}', false, '', '', '', '');

-- Add corresponding profiles
INSERT INTO profiles (
  id, 
  username, 
  full_name, 
  avatar_url, 
  age, 
  gender, 
  bio, 
  location,
  interests,
  communication_preferences,
  photos,
  personalityTraits,
  relationshipPreferences,
  boundaries
) VALUES 
  ('550e8400-e29b-41d4-a716-446655440001', 'alice_johnson', 'Alice Johnson', 'https://images.unsplash.com/photo-1494790108755-2616b612b830', 28, 'female', 'Love hiking, reading, and good coffee. Looking for meaningful connections and someone who appreciates nature.', 'San Francisco, CA', '["hiking", "reading", "coffee", "nature", "photography"]', '["text", "video_call"]', '["https://images.unsplash.com/photo-1494790108755-2616b612b830", "https://images.unsplash.com/photo-1506905925346-21bda4d32df4"]', '["adventurous", "thoughtful", "genuine"]', '["long_term", "meaningful_connection"]', '["respectful_communication", "honesty"]),
  ('550e8400-e29b-41d4-a716-446655440002', 'bob_smith', 'Bob Smith', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e', 32, 'male', 'Software engineer by day, chef by night. Enjoy traveling and trying new cuisines. Always up for a good conversation about tech or travel.', 'New York, NY', '["cooking", "travel", "technology", "food", "movies"]', '["text", "voice_call"]', '["https://images.unsplash.com/photo-1472099645785-5658abf4ff4e", "https://images.unsplash.com/photo-1414235077428-338989a2e8c0"]', '["creative", "analytical", "social"]', '["casual_dating", "friendship"]', '["open_communication", "mutual_respect"]),
  ('550e8400-e29b-41d4-a716-446655440003', 'carol_davis', 'Carol Davis', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80', 26, 'female', 'Artist and yoga instructor. Passionate about creativity and mindfulness. Looking for someone who values personal growth and authentic connection.', 'Los Angeles, CA', '["art", "yoga", "meditation", "creativity", "music"]', '["text", "video_call"]', '["https://images.unsplash.com/photo-1438761681033-6461ffad8d80", "https://images.unsplash.com/photo-1506905925346-21bda4d32df4"]', '["creative", "mindful", "empathetic"]', '["long_term", "spiritual_connection"]', '["emotional_support", "personal_space"]),
  ('550e8400-e29b-41d4-a716-446655440004', 'david_wilson', 'David Wilson', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d', 30, 'male', 'Marketing professional who loves outdoor adventures. Weekend warrior on the hiking trails. Looking for someone to share adventures with.', 'Denver, CO', '["hiking", "camping", "marketing", "fitness", "dogs"]', '["text", "phone_call"]', '["https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d", "https://images.unsplash.com/photo-1551632811-561732d1e306"]', '["adventurous", "outgoing", "reliable"]', '["long_term", "adventure_partner"]', '["active_lifestyle", "honesty"]),
  ('550e8400-e29b-41d4-a716-446655440005', 'emma_garcia', 'Emma Garcia', 'https://images.unsplash.com/photo-**********-94ddf0286df2', 24, 'female', 'Graduate student studying psychology. Love books, coffee shops, and deep conversations. Seeking someone intellectually curious and emotionally mature.', 'Boston, MA', '["psychology", "books", "coffee", "learning", "podcasts"]', '["text", "video_call"]', '["https://images.unsplash.com/photo-**********-94ddf0286df2", "https://images.unsplash.com/photo-1481627834876-b7833e8f5570"]', '["intellectual", "curious", "compassionate"]', '["long_term", "intellectual_connection"]', '["deep_conversations", "emotional_maturity"]),
  ('550e8400-e29b-41d4-a716-446655440006', 'testuser', 'Test User', 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde', 29, 'non-binary', 'This is a test profile for development purposes. Interested in various activities and open to meeting new people.', 'Seattle, WA', '["testing", "development", "technology", "music", "art"]', '["text", "video_call"]', '["https://images.unsplash.com/photo-1535713875002-d1d0cf377fde"]', '["flexible", "understanding", "tech-savvy"]', '["friendship", "casual_dating"]', '["respect", "communication"]);
