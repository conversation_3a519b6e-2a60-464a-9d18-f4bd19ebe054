-- Add 'willing_to_contact' to the tag_type check constraint
ALTER TABLE user_interest_tags DROP CONSTRAINT IF EXISTS user_interest_tags_tag_type_check;

ALTER TABLE user_interest_tags ADD CONSTRAINT user_interest_tags_tag_type_check 
CHECK (tag_type IN ('interested', 'very_interested', 'potential_match', 'willing_to_contact'));

-- Add new notification type for willing to contact
ALTER TABLE user_notifications DROP CONSTRAINT IF EXISTS user_notifications_type_check;

ALTER TABLE user_notifications ADD CONSTRAINT user_notifications_type_check 
CHECK (type IN ('interest_tag', 'contact_willingness', 'mutual_interest', 'willing_to_contact'));
