import {
  Profile,
  Message,
  Conversation,
  DigitalTwin,
  Interest,
  PersonalityTrait,
  RelationshipPreference,
} from "../types";

// ────────────────────────────────────────────────────────────────────────────────
// Canonical lookup tables (objects)
// ────────────────────────────────────────────────────────────────────────────────

export const demoPersonalityTraits: PersonalityTrait[] = [
  { id: "trait1", name: "Empathetic", value: 9 },
  { id: "trait2", name: "<PERSON><PERSON><PERSON>", value: 8 },
  { id: "trait3", name: "Thoughtful", value: 8 },
  { id: "trait4", name: "<PERSON>", value: 8 },
];

export const demoInterests: Interest[] = [
  { id: "int1", name: "Psychology", category: "Science" },
  { id: "int2", name: "Technology", category: "Professional" },
  { id: "int3", name: "Art", category: "Creative" },
  { id: "int4", name: "Reading", category: "Hobbies" },
];

export const demoRelationshipPrefs: RelationshipPreference[] = [
  { id: "pref1", type: "friendship", importance: 8 },
];

// ────────────────────────────────────────────────────────────────────────────────
// Demo Digital Twin - Using proper UUID format
// ────────────────────────────────────────────────────────────────────────────────

export const demoChatTwin: DigitalTwin = {
  // Using a proper UUID that matches the one in the SQL script
  id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  name: "Sophie",
  avatar: "",
  age: 26,
  gender: "Female",
  bio: "I'm Sophie, your digital companion who loves exploring psychology, technology, and creative pursuits. I enjoy thoughtful conversations and helping people discover meaningful connections.",
  // Using actual PersonalityTrait and Interest objects
  personalityTraits: [
    demoPersonalityTraits[0], // Empathetic
    demoPersonalityTraits[1], // Curious  
    demoPersonalityTraits[2], // Thoughtful
  ],
  interests: [
    demoInterests[0], // Psychology
    demoInterests[1], // Technology
    demoInterests[2], // Art
    demoInterests[3], // Reading
  ],
  communicationStyle: ["Empathetic", "Thoughtful", "Encouraging"],
  relationshipGoals: ["Meaningful connections", "Personal growth", "Mutual understanding"],
  compatibilityScore: 85,
  engagementScore: 1,
  accuracyLevel: "High"
};

// ────────────────────────────────────────────────────────────────────────────────
// Demo Profile (full objects expected by Profile interface)
// ────────────────────────────────────────────────────────────────────────────────

export const demoUser: Profile = {
  id: "demo-user",
  name: "Demo User",
  age: 28,
  gender: "Not specified",
  bio: "Exploring digital connections and meaningful conversations",
  // Profile interface expects full objects
  personalityTraits: [
    demoPersonalityTraits[3], // Friendly
    demoPersonalityTraits[1], // Curious
  ],
  interests: [
    demoInterests[1], // Technology
    demoInterests[3], // Reading
  ],
  relationshipPreferences: [demoRelationshipPrefs[0]],
  boundaries: ["Respectful communication", "Privacy"],
  createdAt: new Date(),
  updatedAt: new Date(),
};

// ────────────────────────────────────────────────────────────────────────────────
// Seed messages & conversation
// ────────────────────────────────────────────────────────────────────────────────

export const demoMessages: Message[] = [
  {
    id: "msg1",
    conversationId: "demo-conv",
    sender: "twin",
    senderId: demoChatTwin.id,
    content:
      "Hi there! I'm Sophie. I noticed you're interested in technology and reading. What kind of books do you enjoy?",
    timestamp: new Date(Date.now() - 300_000), // 5 min ago
    reactions: [],
  },
];

export const demoConversation: Conversation = {
  id: "demo-conv",
  userId: demoUser.id,
  type: "twin",
  digitalTwin: demoChatTwin,
  messages: demoMessages,
  createdAt: new Date(Date.now() - 300_000),
  lastActive: new Date(Date.now() - 300_000),
  engagementScore: 1,
};