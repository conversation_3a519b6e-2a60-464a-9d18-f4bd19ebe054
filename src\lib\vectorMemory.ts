import { supabase } from './supabase';

// Replace with your embedding API endpoint and key
const EMBEDDING_API_URL = 'https://api.deepseek.com/v1/embeddings';
const EMBEDDING_API_KEY = import.meta.env.VITE_DEEPSEEK_API_KEY;

export async function getEmbedding(text: string): Promise<number[]> {
  // Use DeepSeek embedding endpoint (or OpenAI if you prefer)
  const res = await fetch(EMBEDDING_API_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${EMBEDDING_API_KEY}`
    },
    body: JSON.stringify({
      model: 'deepseek-embedding',
      input: text
    })
  });
  if (!res.ok) throw new Error('Failed to get embedding');
  const data = await res.json();
  return data.data[0].embedding;
}

export async function saveUserMemoryVector(userId: string, content: string) {
  const embedding = await getEmbedding(content);
  const { data, error } = await supabase
    .from('user_memory_vectors')
    .insert([{ user_id: userId, content, embedding }]);
  if (error) throw error;
  return data;
}

export async function getRelevantUserMemories(userId: string, query: string, topN = 5): Promise<string[]> {
  const queryEmbedding = await getEmbedding(query);
  // Supabase pgvector cosine similarity search
  const { data, error } = await supabase.rpc('match_user_memories', {
    user_id: userId,
    query_embedding: queryEmbedding,
    match_count: topN
  });
  if (error) throw error;
  return data?.map((row: any) => row.content) || [];
}
