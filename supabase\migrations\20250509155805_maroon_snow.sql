/*
  # Add Digital Twin Management Tables

  1. New Tables
    - `user_twins`
      - Digital twin configurations and personality settings
    - `twin_chat_history`
      - Chat history specific to twin interactions
    - `twin_training_data`
      - Training data for digital twins

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Enable vector extension for embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Create user_twins table
CREATE TABLE IF NOT EXISTS user_twins (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) NOT NULL,
  personality_description text NOT NULL,
  conversation_style jsonb NOT NULL,
  interests jsonb NOT NULL,
  behavioral_settings jsonb NOT NULL,
  training_status text DEFAULT 'pending',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create twin_chat_history table
CREATE TABLE IF NOT EXISTS twin_chat_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id),
  twin_id uuid REFERENCES user_twins(id),
  message text NOT NULL,
  message_vector vector(1536),
  timestamp timestamptz DEFAULT now(),
  is_twin_message boolean DEFAULT false,
  encrypted_content text,
  encryption_key_id uuid
);

-- Create twin_training_data table
CREATE TABLE IF NOT EXISTS twin_training_data (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) NOT NULL,
  data_source text NOT NULL,
  content text NOT NULL,
  processed_status text DEFAULT 'pending',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE user_twins ENABLE ROW LEVEL SECURITY;
ALTER TABLE twin_chat_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE twin_training_data ENABLE ROW LEVEL SECURITY;

-- User twins policies
CREATE POLICY "Users can manage their own twins"
  ON user_twins FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Chat history policies
CREATE POLICY "Users can view their chat history"
  ON twin_chat_history FOR SELECT
  TO authenticated
  USING (user_id = auth.uid() OR EXISTS (
    SELECT 1 FROM user_twins
    WHERE user_twins.id = twin_chat_history.twin_id
    AND user_twins.user_id = auth.uid()
  ));

CREATE POLICY "Users can send messages"
  ON twin_chat_history FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Training data policies
CREATE POLICY "Users can manage their training data"
  ON twin_training_data FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create indexes for better query performance
CREATE INDEX twin_chat_history_user_idx ON twin_chat_history(user_id);
CREATE INDEX twin_chat_history_twin_idx ON twin_chat_history(twin_id);
CREATE INDEX twin_chat_history_timestamp_idx ON twin_chat_history(timestamp);
CREATE INDEX twin_training_data_user_idx ON twin_training_data(user_id);