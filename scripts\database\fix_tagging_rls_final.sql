-- Comprehensive fix for user_interest_tags RLS policies
-- This script will fix the RLS policy violations

-- First, let's check current policies and drop them
DROP POLICY IF EXISTS "Users can view interest tags they created or received" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can create interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can update their own interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can delete their own interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable update for tag creators" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable delete for tag creators" ON public.user_interest_tags;

-- Create new, more permissive policies for user_interest_tags
CREATE POLICY "Allow authenticated users to read all tags" ON public.user_interest_tags
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow authenticated users to insert tags" ON public.user_interest_tags
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL 
    AND auth.uid()::text = tagger_id::text
    AND EXISTS (SELECT 1 FROM public.profiles WHERE id = tagged_user_id)
  );

CREATE POLICY "Allow users to update their own tags" ON public.user_interest_tags
  FOR UPDATE USING (auth.uid()::text = tagger_id::text);

CREATE POLICY "Allow users to delete their own tags" ON public.user_interest_tags
  FOR DELETE USING (auth.uid()::text = tagger_id::text);

-- Also ensure the profiles table has proper policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for profile owners" ON public.profiles;

CREATE POLICY "Allow authenticated users to read profiles" ON public.profiles
  FOR SELECT USING (true); -- Allow reading all profiles for discovery

CREATE POLICY "Allow users to insert their own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid()::text = id::text);

CREATE POLICY "Allow users to update their own profile" ON public.profiles
  FOR UPDATE USING (auth.uid()::text = id::text);

-- Fix contact_willingness_status policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Enable update for owners" ON public.contact_willingness_status;

CREATE POLICY "Allow authenticated users to read contact status" ON public.contact_willingness_status
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow users to manage their contact status" ON public.contact_willingness_status
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Fix user_notifications policies
DROP POLICY IF EXISTS "Enable read access for notification owners" ON public.user_notifications;
DROP POLICY IF EXISTS "Enable insert for system and users" ON public.user_notifications;
DROP POLICY IF EXISTS "Enable update for notification owners" ON public.user_notifications;

CREATE POLICY "Allow users to read their notifications" ON public.user_notifications
  FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Allow system to create notifications" ON public.user_notifications
  FOR INSERT WITH CHECK (true); -- Allow system to create notifications

CREATE POLICY "Allow users to update their notifications" ON public.user_notifications
  FOR UPDATE USING (auth.uid()::text = user_id::text);

SELECT 'RLS policies updated successfully' as status;
