import { verifyConnection } from './supabase';

export const performSystemCheck = async () => {
  const results = {
    database: await verifyConnection()
  };

  const issues = Object.entries(results)
    .filter(([_, result]) => result.status === 'error')
    .map(([key, result]) => ({
      component: key,
      error: result.error || 'Check failed'
    }));

  return {
    success: issues.length === 0,
    issues,
    results
  };
};

export const validateEnvironmentVariables = () => {
  const required = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];
  const optional = ['VITE_AVATARS_BUCKET'];

  const missing = required.filter(key => !import.meta.env[key]);
  const missingOptional = optional.filter(key => !import.meta.env[key]);

  return {
    success: missing.length === 0,
    missing,
    optionalMissing: missingOptional
  };
};