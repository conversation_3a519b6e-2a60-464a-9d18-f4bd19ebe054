-- SAFE SOULTWINSYNC DATABASE FIX
-- Apply this if the main script fails due to column issues
-- This script is more conservative and handles missing columns gracefully

-- ===================================================================
-- STEP 1: CREATE MISSING TABLES
-- ===================================================================

-- Create user_interest_tags table
CREATE TABLE IF NOT EXISTS public.user_interest_tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tagger_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  tagged_user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  tag_type text NOT NULL CHECK (tag_type IN ('interested', 'very_interested', 'potential_match')),
  emoji text,
  created_at timestamptz DEFAULT now(),
  UNIQUE(tagger_id, tagged_user_id)
);

-- Create contact_willingness_status table
CREATE TABLE IF NOT EXISTS public.contact_willingness_status (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  is_open_to_contact boolean DEFAULT false,
  status_message text,
  updated_at timestamptz DEFAULT now()
);

-- Create user_notifications table
CREATE TABLE IF NOT EXISTS public.user_notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  type text NOT NULL CHECK (type IN ('interest_tag', 'contact_willingness', 'mutual_interest')),
  title text NOT NULL,
  message text NOT NULL,
  data jsonb,
  read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE public.user_interest_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_willingness_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;

-- ===================================================================
-- STEP 2: ADD MISSING COLUMNS TO PROFILES (SAFE APPROACH)
-- ===================================================================

-- Check and add columns one by one with error handling
DO $$
BEGIN
    -- Add location column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'location') THEN
        ALTER TABLE public.profiles ADD COLUMN location TEXT DEFAULT NULL;
    END IF;
    
    -- Add personality test columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'extraversion_score') THEN
        ALTER TABLE public.profiles ADD COLUMN extraversion_score INTEGER DEFAULT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'agreeableness_score') THEN
        ALTER TABLE public.profiles ADD COLUMN agreeableness_score INTEGER DEFAULT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'conscientiousness_score') THEN
        ALTER TABLE public.profiles ADD COLUMN conscientiousness_score INTEGER DEFAULT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'neuroticism_score') THEN
        ALTER TABLE public.profiles ADD COLUMN neuroticism_score INTEGER DEFAULT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'openness_score') THEN
        ALTER TABLE public.profiles ADD COLUMN openness_score INTEGER DEFAULT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'personality_test_completed_at') THEN
        ALTER TABLE public.profiles ADD COLUMN personality_test_completed_at TIMESTAMPTZ DEFAULT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'personality_insights') THEN
        ALTER TABLE public.profiles ADD COLUMN personality_insights JSONB DEFAULT NULL;
    END IF;
    
END $$;

-- ===================================================================
-- STEP 3: FIX ALL RLS POLICIES
-- ===================================================================

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view interest tags they created or received" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can create interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can update their own interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can delete their own interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable update for tag creators" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable delete for tag creators" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Allow authenticated users to read all tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Allow authenticated users to insert tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Allow users to update their own tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Allow users to delete their own tags" ON public.user_interest_tags;

-- Create new permissive policies for user_interest_tags
CREATE POLICY "Allow authenticated users to read all tags" ON public.user_interest_tags
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow authenticated users to insert tags" ON public.user_interest_tags
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL 
    AND auth.uid()::text = tagger_id::text
    AND EXISTS (SELECT 1 FROM public.profiles WHERE id = tagged_user_id)
  );

CREATE POLICY "Allow users to update their own tags" ON public.user_interest_tags
  FOR UPDATE USING (auth.uid()::text = tagger_id::text);

CREATE POLICY "Allow users to delete their own tags" ON public.user_interest_tags
  FOR DELETE USING (auth.uid()::text = tagger_id::text);

-- Fix profiles table policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for profile owners" ON public.profiles;
DROP POLICY IF EXISTS "Allow authenticated users to read profiles" ON public.profiles;
DROP POLICY IF EXISTS "Allow users to insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Allow users to update their own profile" ON public.profiles;

CREATE POLICY "Allow authenticated users to read profiles" ON public.profiles
  FOR SELECT USING (true); -- Allow reading all profiles for discovery

CREATE POLICY "Allow users to insert their own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid()::text = id::text);

CREATE POLICY "Allow users to update their own profile" ON public.profiles
  FOR UPDATE USING (auth.uid()::text = id::text);

-- Fix contact_willingness_status policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Enable update for owners" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Allow authenticated users to read contact status" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Allow users to manage their contact status" ON public.contact_willingness_status;

CREATE POLICY "Allow authenticated users to read contact status" ON public.contact_willingness_status
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow users to manage their contact status" ON public.contact_willingness_status
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Fix user_notifications policies
DROP POLICY IF EXISTS "Enable read access for notification owners" ON public.user_notifications;
DROP POLICY IF EXISTS "Enable insert for system and users" ON public.user_notifications;
DROP POLICY IF EXISTS "Enable update for notification owners" ON public.user_notifications;
DROP POLICY IF EXISTS "Allow users to read their notifications" ON public.user_notifications;
DROP POLICY IF EXISTS "Allow system to create notifications" ON public.user_notifications;
DROP POLICY IF EXISTS "Allow users to update their notifications" ON public.user_notifications;

CREATE POLICY "Allow users to read their notifications" ON public.user_notifications
  FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Allow system to create notifications" ON public.user_notifications
  FOR INSERT WITH CHECK (true); -- Allow system to create notifications

CREATE POLICY "Allow users to update their notifications" ON public.user_notifications
  FOR UPDATE USING (auth.uid()::text = user_id::text);

-- ===================================================================
-- STEP 4: ADD SAFE TEST DATA
-- ===================================================================

-- Add minimal test profiles (only with columns that definitely exist)
INSERT INTO public.profiles (
  id, 
  username, 
  full_name, 
  avatar_url, 
  age, 
  gender, 
  bio,
  created_at,
  updated_at
) VALUES 
  ('550e8400-e29b-41d4-a716-************', 'roberto_test1', 'Roberto Martinez', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e', 29, 'Male', 'Software developer from Spain. Love technology and meeting new people.', NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-************', 'roberto_test2', 'Roberto Silva', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d', 27, 'Male', 'Designer and entrepreneur from Brazil. Passionate about creativity and innovation.', NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-************', 'alice_johnson', 'Alice Johnson', 'https://images.unsplash.com/photo-1494790108755-2616b612b830', 28, 'Female', 'Love hiking, reading, and good coffee. Looking for meaningful connections.', NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-446655440004', 'bob_smith', 'Bob Smith', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e', 32, 'Male', 'Software engineer by day, chef by night. Enjoy traveling and trying new cuisines.', NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-446655440005', 'carol_davis', 'Carol Davis', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80', 26, 'Female', 'Artist and yoga instructor. Passionate about creativity and mindfulness.', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Try to update with location and other fields if columns exist
DO $$
BEGIN
    -- Update with location if the column exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'location') THEN
        UPDATE public.profiles SET location = 'Madrid, Spain' WHERE id = '550e8400-e29b-41d4-a716-************';
        UPDATE public.profiles SET location = 'São Paulo, Brazil' WHERE id = '550e8400-e29b-41d4-a716-************';
        UPDATE public.profiles SET location = 'San Francisco, CA' WHERE id = '550e8400-e29b-41d4-a716-************';
        UPDATE public.profiles SET location = 'New York, NY' WHERE id = '550e8400-e29b-41d4-a716-446655440004';
        UPDATE public.profiles SET location = 'Los Angeles, CA' WHERE id = '550e8400-e29b-41d4-a716-446655440005';
    END IF;
    
    -- Update with interests if the column exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'interests') THEN
        UPDATE public.profiles SET interests = '["technology", "programming", "travel", "food"]'::jsonb WHERE id = '550e8400-e29b-41d4-a716-************';
        UPDATE public.profiles SET interests = '["design", "business", "art", "music"]'::jsonb WHERE id = '550e8400-e29b-41d4-a716-************';
        UPDATE public.profiles SET interests = '["hiking", "reading", "coffee", "nature"]'::jsonb WHERE id = '550e8400-e29b-41d4-a716-************';
        UPDATE public.profiles SET interests = '["cooking", "travel", "technology", "food"]'::jsonb WHERE id = '550e8400-e29b-41d4-a716-446655440004';
        UPDATE public.profiles SET interests = '["art", "yoga", "meditation", "creativity"]'::jsonb WHERE id = '550e8400-e29b-41d4-a716-446655440005';
    END IF;
    
    -- Update with communication_preferences if the column exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'communication_preferences') THEN
        UPDATE public.profiles SET communication_preferences = '["text", "video_call"]'::text[] WHERE id = '550e8400-e29b-41d4-a716-************';
        UPDATE public.profiles SET communication_preferences = '["text", "voice_call"]'::text[] WHERE id = '550e8400-e29b-41d4-a716-************';
        UPDATE public.profiles SET communication_preferences = '["text", "video_call"]'::text[] WHERE id = '550e8400-e29b-41d4-a716-************';
        UPDATE public.profiles SET communication_preferences = '["text", "voice_call"]'::text[] WHERE id = '550e8400-e29b-41d4-a716-446655440004';
        UPDATE public.profiles SET communication_preferences = '["text", "video_call"]'::text[] WHERE id = '550e8400-e29b-41d4-a716-446655440005';
    END IF;
END $$;

-- ===================================================================
-- STEP 5: VERIFICATION
-- ===================================================================

SELECT 'SAFE DATABASE FIX COMPLETED! 🎉' as status;

-- Show what we have
SELECT 'Tables created:' as info;
SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename IN ('user_interest_tags', 'contact_willingness_status', 'user_notifications');

SELECT 'Test profiles added:' as info;
SELECT id, username, full_name, age, gender FROM public.profiles WHERE id LIKE '550e8400-e29b-41d4-a716%';

SELECT 'Profile columns available:' as info;
SELECT column_name FROM information_schema.columns WHERE table_name = 'profiles' AND table_schema = 'public' ORDER BY column_name;
