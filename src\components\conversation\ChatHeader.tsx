// src/components/chat/ChatHeader.tsx
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'lucide-react';
import { AccuracyIndicator } from './AccuracyIndicator';

interface ChatHeaderProps {
  digitalTwin: { name: string; accuracyLevel?: any };
  messageCount: number;
  lastActive: Date;
  currentType: 'twin' | 'real_user';
  onSwitch: (type: 'twin' | 'real_user') => void;
  onOpenProfile?: () => void;
  onToggleSwitch?: () => void;
}

export default function ChatHeader({
  digitalTwin,
  messageCount,
  lastActive,
  currentType,
  onSwitch,
  onOpenProfile,
  onToggleSwitch,
}: ChatHeaderProps) {
  const isTwin = currentType === 'twin';

  return (
    <div className="flex items-center justify-between mb-4 w-full">
      {/* Avatar + name + stats */}
      <div
        className={`flex items-center ${onOpenProfile ? 'cursor-pointer' : ''}`}
        onClick={onOpenProfile}
      >
        <div className="w-10 h-10 rounded-full mr-3 overflow-hidden">
          {isTwin && (digitalTwin as any).avatar ? (
            <img
              src={(digitalTwin as any).avatar}
              alt={digitalTwin.name}
              className="w-full h-full object-cover"
            />
          ) : !isTwin && (digitalTwin as any).avatar_url ? (
            <img
              src={(digitalTwin as any).avatar_url}
              alt="User"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white font-bold">
              {isTwin ? digitalTwin.name.charAt(0) : 'U'}
            </div>
          )}
        </div>
        <div>
          <h1 className="font-semibold text-gray-800">
            {isTwin ? digitalTwin.name : 'Real User'}
          </h1>
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <span>{messageCount} messages</span>
            <span>•</span>
            <span>Last active {lastActive.toLocaleTimeString()}</span>
            {isTwin && digitalTwin.accuracyLevel != null && (
              <AccuracyIndicator accuracyLevel={digitalTwin.accuracyLevel} />
            )}
          </div>
        </div>
      </div>

      {/* Controls: Icon toggle + Two-option segment */}
      <div className="flex items-center space-x-2">
        {/* Optional smaller icon toggle */}
        {onToggleSwitch && (
          <button
            onClick={onToggleSwitch}
            className="text-gray-600 hover:text-gray-800 p-1"
            aria-label={
              isTwin
                ? 'Switch to real user chat'
                : 'Switch to digital twin chat'
            }
            title={
              isTwin
                ? 'Chat with real user'
                : 'Chat with digital twin'
            }
          >
            {isTwin ? (
              <User className="h-5 w-5" />
            ) : (
              <Bot className="h-5 w-5" />
            )}
          </button>
        )}

        {/* Twin vs. Real User segment */}
        <div className="flex rounded-lg overflow-hidden border border-gray-300">
          <button
            onClick={() => onSwitch('twin')}
            className={`flex items-center px-3 py-1 space-x-1 ${
              isTwin
                ? 'bg-white text-gray-900'
                : 'bg-gray-100 text-gray-600 hover:bg-white'
            }`}
          >
            <Bot className="h-4 w-4" />
            <span className="text-sm font-medium">Twin</span>
          </button>
          <button
            onClick={() => onSwitch('real_user')}
            className={`flex items-center px-3 py-1 space-x-1 ${
              !isTwin
                ? 'bg-white text-gray-900'
                : 'bg-gray-100 text-gray-600 hover:bg-white'
            }`}
          >
            <User className="h-4 w-4" />
            <span className="text-sm font-medium">Real User</span>
          </button>
        </div>
      </div>
    </div>
  );
}
