/*
  # Add Profile Fields

  1. Changes
    - Add new columns to profiles table for user details
    - Add indexes for performance optimization
    
  2. New Columns
    - age: User's age
    - gender: User's gender identity
    - bio: User's biography
    - personality_traits: Array of personality traits
    - communication_style: Preferred communication styles
    - interests: User's interests and hobbies
    - relationship_preferences: Dating preferences
    - boundaries: User's boundaries and limits
*/

-- Add new columns to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS age integer;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS gender text;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bio text;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS personality_traits jsonb DEFAULT '[]'::jsonb;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS communication_style jsonb DEFAULT '[]'::jsonb;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS interests jsonb DEFAULT '[]'::jsonb;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS relationship_preferences jsonb DEFAULT '[]'::jsonb;
<PERSON>TER TABLE profiles ADD COLUMN IF NOT EXISTS boundaries jsonb DEFAULT '[]'::jsonb;

-- Add btree index for username searches
CREATE INDEX IF NOT EXISTS profiles_username_search_idx ON profiles (username);

-- Add GIN indexes for JSON fields
CREATE INDEX IF NOT EXISTS profiles_interests_idx ON profiles USING gin (interests);
CREATE INDEX IF NOT EXISTS profiles_personality_traits_idx ON profiles USING gin (personality_traits);