-- Installs pg_cron (if missing) and schedules a daily job
-- that deletes expired chats + their messages at 04:00 UTC.

create extension if not exists pg_cron;

do $$
begin
  -- only register once, and only if conversations table exists
  if not exists (
    select 1 from cron.job where jobname = 'daily_chat_cleanup'
  ) and exists (
    select from information_schema.tables where table_schema = 'public' and table_name = 'conversations'
  ) then
    perform cron.schedule(
      job_name => 'daily_chat_cleanup',
      schedule => '0 4 * * *',
      command  => $CMD$

        delete from public.messages
        where conversation_id in (
          select id
          from   public.conversations
          where  keep_alive_until < now()
        );

        delete from public.conversations
        where keep_alive_until < now();
      $CMD$
    );
  end if;
end$$;
