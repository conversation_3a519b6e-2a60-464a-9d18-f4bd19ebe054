import React from 'react';
import { X } from 'lucide-react';
import { Profile, Interest } from '../../types';

interface UserProfileProps {
  user: Profile;
  onClose: () => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ user, onClose }) => {
  // Convert interests to string[] for display
  let interests: string[] = [];
  if (user.interests && Array.isArray(user.interests)) {
    interests = (user.interests as Interest[]).map(i => typeof i === 'string' ? i : i.name);
  }

  return (
    <div className="h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-xl font-semibold">Profile</h2>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700"
          aria-label="Close profile"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      
      <div className="p-4">
        <div className="flex items-center mb-6">
          {user.avatar_url ? (
            <img
              src={user.avatar_url}
              alt={user.name}
              className="w-16 h-16 rounded-full object-cover mr-4"
            />
          ) : (
            <div className="w-16 h-16 rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white text-2xl font-bold mr-4">
              {user.name.charAt(0)}
            </div>
          )}
          <div>
            <h3 className="text-xl font-semibold">{user.name}</h3>
          </div>
        </div>

        {user.bio && (
          <div className="mb-6">
            <h4 className="text-sm font-semibold text-gray-500 mb-2">About</h4>
            <p className="text-gray-700">{user.bio}</p>
          </div>
        )}

        {interests && interests.length > 0 && (
          <div>
            <h4 className="text-sm font-semibold text-gray-500 mb-2">Interests</h4>
            <div className="flex flex-wrap gap-2">
              {interests.map((interest, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-gray-100 rounded-full text-sm text-gray-700"
                >
                  {interest}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserProfile;