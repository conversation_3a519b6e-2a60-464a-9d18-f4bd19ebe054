{"name": "soultwin-sync", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "NODE_OPTIONS=--experimental-vm-modules VITE_SUPABASE_URL=https://example.com VITE_SUPABASE_ANON_KEY=anon VITE_DEEPSEEK_API_KEY=dummy jest --config jest.config.cjs", "test:watch": "NODE_OPTIONS=--experimental-vm-modules VITE_SUPABASE_URL=https://example.com VITE_SUPABASE_ANON_KEY=anon VITE_DEEPSEEK_API_KEY=dummy jest --watch --config jest.config.cjs"}, "dependencies": {"@headlessui/react": "^2.2.2", "@supabase/supabase-js": "^2.39.7", "@tanstack/react-query": "^5.24.1", "crypto-js": "^4.2.0", "date-fns": "^3.3.1", "lucide-react": "^0.344.0", "nanoid": "^5.0.6", "openai": "^4.98.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.22.3", "zustand": "^4.5.1"}, "devDependencies": {"@jest/globals": "^30.0.0-beta.3", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/node": "^22.15.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "jest": "^29.7.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.4", "typescript": "^5.3.3", "vite": "^5.4.19"}}