-- Fix user discovery by creating missing profiles for authenticated users
-- This bypasses RLS and creates profiles for all auth users who are missing them

-- First, check current RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'profiles';

-- Insert profiles for your 3 authenticated users based on the screenshot
INSERT INTO public.profiles (
  id, 
  username, 
  full_name, 
  age, 
  gender, 
  bio,
  created_at,
  updated_at
) VALUES 
  -- User 1: <EMAIL> (ID from your auth table)
  ('1818c8d3-c9a1-4a53-9b01-fc51e45ce55d', 'roberto.coscia.it', '<PERSON>', 30, 'male', '<PERSON> from Italy. Tech enthusiast and entrepreneur looking for meaningful connections.', NOW(), NOW()),
  
  -- User 2: <EMAIL> (ID from your auth table)
  ('c6f6f365-6150-4bf2-96ee-39be2ec41094', 'roberto_coscia_tech', '<PERSON>', 28, 'male', '<PERSON> from <EMAIL>. Love technology and innovation.', NOW(), NOW()),
  
  -- User 3: robert<PERSON><PERSON><EMAIL> (ID from your auth table)  
  ('95ad2466-a1b7-4212-ade0-fcf73bc0f5d4', 'roberto_coscia_25', 'Roberto Coscia', 25, 'male', 'Roberto <NAME_EMAIL>. Passionate about connecting with like-minded people.', NOW(), NOW())

ON CONFLICT (id) DO UPDATE SET
  username = EXCLUDED.username,
  full_name = EXCLUDED.full_name,
  age = EXCLUDED.age,
  gender = EXCLUDED.gender,
  bio = EXCLUDED.bio,
  updated_at = NOW();

-- Verify the profiles were created
SELECT 'CREATED PROFILES:' as status;
SELECT id, username, full_name, age, gender, created_at 
FROM public.profiles 
ORDER BY created_at DESC;

-- Test search for "rob" (should find all 3 Roberto users)
SELECT 'SEARCH TEST - "rob":' as status;
SELECT id, username, full_name, age, gender 
FROM public.profiles 
WHERE full_name ILIKE '%rob%' OR username ILIKE '%rob%'
ORDER BY full_name;

-- Test browse profiles (exclude one user to simulate discovery)
SELECT 'BROWSE TEST (excluding first user):' as status;
SELECT id, username, full_name, age, gender 
FROM public.profiles 
WHERE id != '1818c8d3-c9a1-4a53-9b01-fc51e45ce55d'
ORDER BY created_at DESC;

-- Show total counts
SELECT 'SUMMARY:' as status;
SELECT 
  COUNT(*) as total_profiles,
  COUNT(*) FILTER (WHERE full_name ILIKE '%roberto%') as roberto_count
FROM public.profiles;
