# Conversation Boundaries Management System

## Overview

The Conversation Boundaries Management System provides comprehensive privacy and communication controls for users in the SoulTwinSync dating platform. This feature allows users to set detailed preferences about who can contact them, what type of content they're comfortable with, and how they want to manage their interactions.

## Features

### 🛡️ Communication Preferences
- **Message Permissions**: Control who can send messages (everyone, tagged users only, mutual interest, or no one)
- **Introduction Requirements**: Require users to introduce themselves before starting conversations
- **Response Time Expectations**: Set expectations for how quickly you typically respond

### 🚫 Content Filtering
- **Explicit Content Blocking**: Automatically filter inappropriate messages
- **Early Conversation Boundaries**: Block personal questions, meeting requests, or contact sharing too early
- **Custom Boundaries**: Add personalized rules and preferences

### 👤 User Management
- **Block Users**: Prevent specific users from contacting you
- **Report System**: Report inappropriate behavior with detailed categorization
- **Blocked Users Management**: View and manage your blocked users list

### 🔔 Smart Notifications
- **Boundary Violations**: Get notified when someone violates your boundaries
- **Interest Notifications**: Receive alerts when users tag you with interest
- **Mutual Interest Alerts**: Know when there's mutual interest between you and another user

## Database Schema

### Tables Created
1. **conversation_boundaries**: Stores user communication preferences
2. **blocked_users**: Manages blocked user relationships
3. **conversation_reports**: Handles user reports and moderation

### Key Fields
- `allow_messages_from`: Controls message permissions
- `response_time_expectation`: Sets response time preferences
- `block_explicit_content`: Content filtering settings
- `custom_boundaries`: User-defined rules

## Usage

### For Users
1. **Access Settings**: Go to Settings → "Manage conversation boundaries"
2. **Set Preferences**: Configure who can message you and content filters
3. **Manage Blocked Users**: View and unblock users as needed
4. **Report Issues**: Use the report system for inappropriate behavior

### For Developers
```typescript
import { 
  getConversationBoundaries, 
  canUserMessage, 
  blockUser 
} from '../lib/conversationBoundaries';

// Check if user can send a message
const { canMessage, reason } = await canUserMessage(senderId, recipientId);

// Get user's boundaries
const boundaries = await getConversationBoundaries(userId);

// Block a user
const result = await blockUser(blockerId, blockedId, reason);
```

## Components

### Main Components
- `ConversationBoundariesManager`: Main modal for managing all boundaries
- `ConversationBoundariesModal`: Detailed settings configuration
- `BlockedUsersModal`: Blocked users management
- `BlockUserButton`: Quick block/report functionality

### Integration Points
- **Settings Page**: Main entry point for boundary management
- **Profile Views**: Block/report buttons on user profiles
- **Messaging System**: Boundary checks before sending messages

## API Functions

### Core Functions
- `getConversationBoundaries(userId)`: Retrieve user's boundaries
- `updateConversationBoundaries(userId, boundaries)`: Update settings
- `canUserMessage(senderId, recipientId)`: Check messaging permissions
- `blockUser(blockerId, blockedId, reason)`: Block a user
- `reportUser(reporterId, reportedId, type, description)`: Report inappropriate behavior

### Utility Functions
- `checkContentViolation(content, boundaries)`: Validate message content
- `createDefaultBoundaries(userId)`: Set up default settings for new users

## Security Features

### Row Level Security (RLS)
- All tables have RLS enabled
- Users can only access their own data
- Proper authentication checks on all operations

### Data Privacy
- Blocked user relationships are private
- Reports are confidential
- Boundaries are user-specific and secure

## Installation

1. **Run Database Migration**:
   ```sql
   -- Execute the SQL in supabase/migrations/20250607000000_conversation_boundaries.sql
   ```

2. **Import Components**:
   ```typescript
   import ConversationBoundariesManager from '../components/settings/ConversationBoundariesManager';
   ```

3. **Add to Settings Page**:
   ```typescript
   const [isBoundariesModalOpen, setIsBoundariesModalOpen] = useState(false);
   
   // Button to open modal
   <button onClick={() => setIsBoundariesModalOpen(true)}>
     Manage conversation boundaries
   </button>
   
   // Modal component
   <ConversationBoundariesManager
     isOpen={isBoundariesModalOpen}
     onClose={() => setIsBoundariesModalOpen(false)}
   />
   ```

## Error Handling

The system includes comprehensive error handling:
- Graceful fallbacks when database tables don't exist
- User-friendly error messages
- Logging for debugging purposes
- Default permissions when boundaries can't be loaded

## Future Enhancements

- **AI Content Filtering**: Advanced content analysis
- **Availability Windows**: Time-based messaging restrictions
- **Conversation Templates**: Pre-written introduction templates
- **Advanced Reporting**: More detailed report categories
- **Moderation Dashboard**: Admin tools for reviewing reports

## Support

For technical issues or feature requests, please refer to the main SoulTwinSync documentation or contact the development team.
