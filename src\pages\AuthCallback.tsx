import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { handleAuthCallback } from '../lib/auth';
import { supabase } from '../lib/supabase';

export const AuthCallback: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    let processed = false;
    let mounted = true;
    
    const processAuthCallback = async () => {
      if (processed || !mounted) return;
      processed = true;
      
      console.log('Auth callback component mounted');
      
      try {
        // Get URL parameters and log them for debugging
        const urlParams = new URLSearchParams(window.location.search);
        const hasCode = urlParams.has('code');
        const hasAccessToken = urlParams.has('access_token');
        const hasError = urlParams.has('error');
        
        console.log('AuthCallback - URL analysis:', {
          fullUrl: window.location.href,
          search: window.location.search,
          hasCode,
          hasAccessToken,
          hasError,
          code: urlParams.get('code')?.substring(0, 10) + '...',
          error: urlParams.get('error')
        });
        
        // Check for OAuth error first
        if (hasError) {
          const error = urlParams.get('error');
          const errorDescription = urlParams.get('error_description');
          console.error('OAuth error:', error, errorDescription);
          if (mounted) {
            navigate('/login', { state: { error: `OAuth error: ${error}` } });
          }
          return;
        }
        
        // Check for auth parameters
        const hasAuthParams = hasCode || hasAccessToken;
        
        if (!hasAuthParams) {
          console.log('No auth parameters found, checking if this is a direct access');
          // Wait a moment to see if Supabase session exists (user might be already logged in)
          const { data: { session } } = await supabase.auth.getSession();
          if (session) {
            console.log('Found existing session, redirecting to dashboard');
            if (mounted) {
              navigate('/dashboard', { replace: true });
            }
            return;
          }
          
          console.log('No auth parameters and no session, redirecting to login');
          if (mounted) {
            navigate('/login?error=missing_auth_params');
          }
          return;
        }
        
        // Clear URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);
        
        // Wait for Supabase to process the PKCE callback automatically
        let attempts = 0;
        const maxAttempts = 10;
        
        while (attempts < maxAttempts) {
          const { data: { session }, error } = await supabase.auth.getSession();
          
          if (error) {
            console.error('Session error:', error);
            if (mounted) {
              navigate('/login', { state: { error: 'Authentication failed' } });
            }
            return;
          }
          
          if (session) {
            console.log('Session found in callback:', session.user.id);
            
            // Check if user is new or has intended destination
            const intendedPath = localStorage.getItem('intendedPath');
            if (intendedPath) {
              localStorage.removeItem('intendedPath');
              if (mounted) {
                navigate(intendedPath, { replace: true });
              }
              return;
            }
            
            // Check if user profile exists to determine if they're new
            try {
              const { data: profile, error: profileError } = await supabase
                .from('profiles')
                .select('id')
                .eq('id', session.user.id)
                .single();
              
              if (profileError && profileError.code !== 'PGRST116') {
                throw profileError;
              }
              
              const isNewUser = !profile;
              
              if (isNewUser) {
                // Create profile for new user
                const result = await handleAuthCallback();
                if (result.success) {
                  if (mounted) {
                    navigate('/profile/create', { replace: true });
                  }
                } else {
                  if (mounted) {
                    navigate('/login', { state: { error: result.error?.message } });
                  }
                }
              } else {
                // Check if existing user has a basic profile or completed personality test
                const { data: profileData, error: profileFetchError } = await supabase
                  .from('profiles')
                  .select('name, interests, personality_test_completed_at, extraversion_score, agreeableness_score, conscientiousness_score, neuroticism_score, openness_score')
                  .eq('id', session.user.id)
                  .single();
                
                if (profileFetchError && profileFetchError.code !== 'PGRST116') {
                   console.error('Error fetching profile data:', profileFetchError);
                   if (mounted) {
                     navigate('/login', { state: { error: 'Failed to load profile data' } });
                   }
                   return;
                 }
                
                const hasCompletedPersonalityTest = Boolean(
                  profileData?.personality_test_completed_at &&
                  profileData?.extraversion_score !== null &&
                  profileData?.agreeableness_score !== null &&
                  profileData?.conscientiousness_score !== null &&
                  profileData?.neuroticism_score !== null &&
                  profileData?.openness_score !== null
                );
                
                const hasBasicProfile = Boolean(
                   profileData &&
                   profileData.name &&
                   profileData.interests &&
                   profileData.interests.length > 0
                 );
                 
                 console.log('AuthCallback - Profile check:', {
                   hasBasicProfile,
                   hasCompletedPersonalityTest,
                   profileData: profileData ? {
                     name: profileData.name,
                     interests: profileData.interests,
                     personality_test_completed_at: profileData.personality_test_completed_at
                   } : null,
                   profileFetchError
                 });
                 
                 // If user has no basic profile and hasn't completed personality test, redirect to test
                 if (!hasBasicProfile && !hasCompletedPersonalityTest) {
                   console.log('AuthCallback - Redirecting to personality test');
                   if (mounted) {
                     navigate('/personality-test', { replace: true });
                   }
                   return;
                 }
                 
                 console.log('AuthCallback - Redirecting to dashboard');
                
                // Add a small delay to ensure the session is fully established
                 setTimeout(() => {
                   if (mounted) {
                     navigate('/dashboard', { replace: true });
                   }
                 }, 200);
              }
              return;
            } catch (error) {
              console.error('Error checking user profile:', error);
              if (mounted) {
                navigate('/login', { state: { error: 'Failed to verify user profile' } });
              }
              return;
            }
          }
          
          // Wait before next attempt
          await new Promise(resolve => setTimeout(resolve, 500));
          attempts++;
        }
        
        // If we get here, no session was found after all attempts
        console.log('No session found after multiple attempts, redirecting to login');
        if (mounted) {
          navigate('/login', { state: { error: 'Authentication failed' } });
        }
        
      } catch (error) {
        console.error('Auth callback error:', error);
        if (mounted) {
          navigate('/login', { state: { error: 'Authentication failed' } });
        }
      }
    };

    processAuthCallback();
    
    return () => {
      mounted = false;
    };
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
        <p className="text-gray-600">Completing sign in...</p>
      </div>
    </div>
  );
};

export default AuthCallback;