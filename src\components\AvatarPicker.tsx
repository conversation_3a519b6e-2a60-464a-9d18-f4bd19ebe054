import React, { ChangeEvent } from "react";
import { supabase } from "../lib/supabase";

const AVATARS_BUCKET = "avatars";

const MAX_SIZE = 2 * 1024 * 1024; // 2 MB

interface AvatarPickerProps {
  onUploadSuccess?: (newAvatarUrl: string) => void;
}

export const AvatarPicker: React.FC<AvatarPickerProps> = ({
  onUploadSuccess,
}) => {
  const handleChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const validTypes = ["image/png", "image/jpeg", "image/jpg"];

    if (!validTypes.includes(file.type) || file.size > MAX_SIZE) {
      alert("Only PNG / JPEG images up to 2 MB are allowed");
      return;
    }

    try {
      // Get the current user
      const {
        data: { user },
        error: authError
      } = await supabase.auth.getUser();
      
      if (authError || !user) {
        alert("Please log in first");
        console.error("Auth error:", authError);
        return;
      }

      // Generate unique filename
      const ext = file.name.split(".").pop() || "png";
      const sanitizedExt = ext.replace(/[^a-zA-Z0-9]/g, "");
      const filename = `${Date.now()}.${sanitizedExt}`;
      const path = `${user.id}/${filename}`;

      // Upload the file
      const { error: uploadError } = await supabase.storage
        .from(AVATARS_BUCKET)
        .upload(path, file, {
          cacheControl: "3600",
          upsert: true,
          contentType: file.type,
        });

      if (uploadError) {
        console.error("Avatar upload failed:", uploadError);
        
        // Provide more specific error messages
        if (uploadError.message.includes("row-level security")) {
          alert("Permission denied. Please check your storage policies.");
        } else if (uploadError.message.includes("Bucket not found")) {
          alert(`Avatar bucket "${AVATARS_BUCKET}" does not exist. Please create it in Supabase.`);
        } else {
          alert(`Upload failed: ${uploadError.message}`);
        }
        return;
      }

      // Get the public URL
      const { data } = supabase.storage
        .from(AVATARS_BUCKET)
        .getPublicUrl(path);
      
      const avatarUrl = data.publicUrl;

      // Update the user's profile
      const { error: updateError } = await supabase
        .from("profiles")
        .update({ avatar_url: avatarUrl })
        .eq("id", user.id);

      if (updateError) {
        alert(`Error updating profile: ${updateError.message}`);
        return;
      }

      // Success callback
      if (onUploadSuccess) {
        onUploadSuccess(avatarUrl);
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      alert("An unexpected error occurred. Please try again.");
    }
  };

  return (
    <input 
      type="file" 
      accept="image/png,image/jpeg" 
      onChange={handleChange}
      className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"
    />
  );
};