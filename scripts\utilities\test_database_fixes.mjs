// Manual database application script
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVnZ2NjYmtoeHBlbXd1dHNxc3BoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc4MDQ5MDgsImV4cCI6MjA1MzM4MDkwOH0.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyDirectDatabaseFixes() {
    console.log('🔧 Applying database fixes directly...\n');

    try {
        // Check if personality test columns exist
        console.log('1️⃣ Checking existing columns...');
        const { data: columns, error: columnError } = await supabase
            .from('information_schema.columns')
            .select('column_name')
            .eq('table_name', 'profiles')
            .eq('table_schema', 'public');

        if (columnError) {
            console.log('ℹ️ Cannot check columns via information_schema, continuing...');
        } else {
            const existingColumns = columns?.map(c => c.column_name) || [];
            console.log('Existing columns:', existingColumns);
        }

        // Test current profile structure
        console.log('\n2️⃣ Testing current profile structure...');
        const { data: profileTest, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .limit(1);

        if (profileError) {
            console.error('❌ Error testing profiles:', profileError);
        } else if (profileTest && profileTest.length > 0) {
            console.log('✅ Current profile structure:', Object.keys(profileTest[0]));
            
            // Check if personality test columns exist
            const hasPersonalityColumns = profileTest[0].hasOwnProperty('extraversion_score');
            console.log(`Personality test columns exist: ${hasPersonalityColumns}`);
        }

        // Test discovery functionality
        console.log('\n3️⃣ Testing discovery...');
        const { data: discoveryTest, error: discoveryError } = await supabase
            .from('profiles')
            .select('id, username, full_name, age, gender, bio')
            .not('age', 'is', null)
            .not('gender', 'is', null)
            .limit(5);

        if (discoveryError) {
            console.error('❌ Discovery test failed:', discoveryError);
        } else {
            console.log(`✅ Discovery working - found ${discoveryTest?.length || 0} profiles`);
            if (discoveryTest && discoveryTest.length > 0) {
                console.log('Sample profile:', discoveryTest[0]);
            }
        }

        // Test the personality test functionality by trying to update a profile
        console.log('\n4️⃣ Testing personality test update capability...');
        
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
            console.log(`Current user: ${user.id}`);
            
            // Try to update with personality test data
            const { data: updateTest, error: updateError } = await supabase
                .from('profiles')
                .update({
                    extraversion_score: 75,
                    agreeableness_score: 80,
                    conscientiousness_score: 70,
                    neuroticism_score: 30,
                    openness_score: 85,
                    personality_test_completed_at: new Date().toISOString(),
                    personality_insights: {
                        summary: "Test personality insights",
                        strengths: ["Creative", "Analytical"],
                        growth_areas: ["Time management"],
                        communication_style: "Direct and thoughtful",
                        relationship_tendencies: ["Values deep connections"]
                    }
                })
                .eq('id', user.id)
                .select();

            if (updateError) {
                console.error('❌ Personality test update failed:', updateError);
                if (updateError.message.includes('column') && updateError.message.includes('does not exist')) {
                    console.log('⚠️ Personality test columns do not exist in database');
                    console.log('👉 Manual database migration needed');
                }
            } else {
                console.log('✅ Personality test update successful:', updateTest);
            }
        } else {
            console.log('ℹ️ No authenticated user found');
        }

    } catch (error) {
        console.error('❌ Unexpected error:', error);
    }
}

applyDirectDatabaseFixes();
