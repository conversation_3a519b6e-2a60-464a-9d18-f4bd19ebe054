import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVnZ2NjYmtoeHBlbXd1dHNxc3BoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc4MDQ5MDgsImV4cCI6MjA1MzM4MDkwOH0.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function runSQL(filename) {
    try {
        console.log(`📄 Reading ${filename}...`);
        const sql = readFileSync(filename, 'utf8');
        
        console.log(`🚀 Executing SQL from ${filename}...`);
        const { data, error } = await supabase.rpc('run_sql', { query: sql });
        
        if (error) {
            console.error(`❌ Error in ${filename}:`, error);
            return false;
        } else {
            console.log(`✅ Successfully executed ${filename}`);
            if (data) console.log('Result:', data);
            return true;
        }
    } catch (err) {
        console.error(`❌ Failed to process ${filename}:`, err.message);
        return false;
    }
}

async function applyDatabaseFixes() {
    console.log('🔧 Applying database fixes...\n');
    
    // Apply fixes in order
    const scripts = [
        'complete_fix.sql',
        'add_personality_test_columns.sql'
    ];
    
    for (const script of scripts) {
        const success = await runSQL(script);
        if (!success) {
            console.log(`⚠️ Failed to apply ${script}, continuing with next script...`);
        }
        console.log(''); // Add space between scripts
    }
    
    console.log('✅ Database fix process completed!');
}

applyDatabaseFixes();
