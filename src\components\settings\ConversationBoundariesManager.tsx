import React, { useState } from 'react';
import { X, Shield, UserX, AlertTriangle, Settings } from 'lucide-react';
import ConversationBoundariesModal from './ConversationBoundariesModal';
import BlockedUsersModal from './BlockedUsersModal';

interface ConversationBoundariesManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

const ConversationBoundariesManager: React.FC<ConversationBoundariesManagerProps> = ({
  isOpen,
  onClose
}) => {
  const [activeModal, setActiveModal] = useState<'main' | 'boundaries' | 'blocked'>('main');

  const handleOpenBoundaries = () => setActiveModal('boundaries');
  const handleOpenBlocked = () => setActiveModal('blocked');
  const handleBackToMain = () => setActiveModal('main');

  if (!isOpen) return null;

  // Show sub-modals
  if (activeModal === 'boundaries') {
    return (
      <ConversationBoundariesModal
        isOpen={true}
        onClose={handleBackToMain}
      />
    );
  }

  if (activeModal === 'blocked') {
    return (
      <BlockedUsersModal
        isOpen={true}
        onClose={handleBackToMain}
      />
    );
  }

  // Main modal
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-lg w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Shield className="w-6 h-6 text-purple-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-800">Conversation Boundaries</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-gray-600 mb-6">
            Manage your privacy and communication preferences to create a safer, more comfortable experience.
          </p>

          <div className="space-y-4">
            {/* Conversation Settings */}
            <button
              onClick={handleOpenBoundaries}
              className="w-full p-4 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg hover:from-purple-100 hover:to-pink-100 transition-all duration-200 text-left group"
            >
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-105 transition-transform">
                  <Settings className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-1">Communication Preferences</h3>
                  <p className="text-sm text-gray-600">
                    Set who can message you, response expectations, and content filters
                  </p>
                </div>
              </div>
            </button>

            {/* Blocked Users */}
            <button
              onClick={handleOpenBlocked}
              className="w-full p-4 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-lg hover:from-red-100 hover:to-orange-100 transition-all duration-200 text-left group"
            >
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-105 transition-transform">
                  <UserX className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-1">Blocked Users</h3>
                  <p className="text-sm text-gray-600">
                    View and manage users you've blocked from contacting you
                  </p>
                </div>
              </div>
            </button>

            {/* Safety Tips */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-blue-800 mb-2">Safety Tips</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Set clear boundaries about what you're comfortable discussing</li>
                    <li>• Don't share personal information too quickly</li>
                    <li>• Trust your instincts - block or report users who make you uncomfortable</li>
                    <li>• Take your time getting to know someone before meeting in person</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConversationBoundariesManager;
