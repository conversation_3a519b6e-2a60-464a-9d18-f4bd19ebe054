#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// These would normally come from environment variables
// You'll need to replace these with your actual Supabase credentials
const SUPABASE_URL = 'https://your-project.supabase.co';
const SUPABASE_SERVICE_KEY = 'your-service-role-key';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function applySQLScript(scriptPath, description) {
  console.log(`\n🔧 ${description}...`);
  
  try {
    const sqlContent = fs.readFileSync(scriptPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.error(`❌ Error executing statement: ${error.message}`);
          // Try executing directly
          const { error: directError } = await supabase
            .from('_supabase_migrations')  // This will fail but we can catch it
            .select('*');
          
          if (directError) {
            console.log(`Trying alternative execution method...`);
            // Execute using raw SQL
            const { data, error: rawError } = await supabase.rpc('exec_raw_sql', { 
              query: statement 
            });
            
            if (rawError) {
              console.error(`❌ Raw SQL error: ${rawError.message}`);
            } else {
              console.log(`✅ Statement executed successfully`);
            }
          }
        } else {
          console.log(`✅ Statement executed successfully`);
        }
      }
    }
    
    console.log(`✅ ${description} completed`);
    
  } catch (error) {
    console.error(`❌ Error reading ${scriptPath}:`, error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 Starting comprehensive database fixes for SoulTwinSync...');
  
  try {
    // Test connection
    console.log('🔍 Testing Supabase connection...');
    const { data, error } = await supabase.from('profiles').select('id').limit(1);
    if (error) {
      console.error('❌ Connection failed:', error.message);
      console.log('\n📝 Manual instructions:');
      console.log('1. Go to your Supabase project dashboard');
      console.log('2. Navigate to SQL Editor');
      console.log('3. Execute the following scripts in order:');
      console.log('   - complete_fix.sql');
      console.log('   - fix_tagging_rls_final.sql');
      console.log('   - add_personality_test_columns.sql');
      return;
    }
    console.log('✅ Connection successful');
    
    // Apply fixes in order
    await applySQLScript('./complete_fix.sql', 'Creating missing tables and test users');
    await applySQLScript('./fix_tagging_rls_final.sql', 'Fixing RLS policies for tagging system');
    await applySQLScript('./add_personality_test_columns.sql', 'Adding personality test columns');
    
    console.log('\n🎉 All database fixes applied successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Test the discovery page and tagging functionality');
    console.log('3. Test the personality test at /personality-test');
    
  } catch (error) {
    console.error('❌ Failed to apply fixes:', error.message);
    console.log('\n📝 Please apply the fixes manually in Supabase SQL Editor');
  }
}

main().catch(console.error);
