import React from 'react';
import { <PERSON>, User, Bot } from 'lucide-react';

interface SwitchUserModalProps {
  onSwitch: (type: 'twin' | 'real_user') => void;
  onClose: () => void;
  currentType: 'twin' | 'real_user';
}

const SwitchUserModal: React.FC<SwitchUserModalProps> = ({
  onSwitch,
  onClose,
  currentType
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 animate-fadeIn">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">Switch Chat Partner</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            aria-label="Close"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-6">
          <button
            onClick={() => onSwitch('twin')}
            className={`p-4 rounded-lg border-2 transition-all ${
              currentType === 'twin'
                ? 'border-purple-500 bg-purple-50'
                : 'border-gray-200 hover:border-purple-200 hover:bg-purple-50'
            }`}
          >
            <Bot className="w-8 h-8 mx-auto mb-2 text-purple-500" />
            <h3 className="font-medium text-gray-800">Digital Twin</h3>
            <p className="text-sm text-gray-500 mt-1">Chat with AI companion</p>
          </button>

          <button
            onClick={() => onSwitch('real_user')}
            className={`p-4 rounded-lg border-2 transition-all ${
              currentType === 'real_user'
                ? 'border-pink-500 bg-pink-50'
                : 'border-gray-200 hover:border-pink-200 hover:bg-pink-50'
            }`}
          >
            <User className="w-8 h-8 mx-auto mb-2 text-pink-500" />
            <h3 className="font-medium text-gray-800">Real User</h3>
            <p className="text-sm text-gray-500 mt-1">Chat with another person</p>
          </button>
        </div>

        <div className="text-sm text-gray-500">
          Note: Switching chat partners will create a new conversation.
        </div>
      </div>
    </div>
  );
};

export default SwitchUserModal;