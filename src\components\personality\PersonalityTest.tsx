import React, { useState } from 'react';
import { Brain, CheckCircle, ArrowRight, ArrowLeft, Star, Heart, Zap, Target, MessageCircle } from 'lucide-react';

interface PersonalityQuestion {
  id: string;
  text: string;
  description?: string;
  category: 'extraversion' | 'openness' | 'conscientiousness' | 'agreeableness' | 'neuroticism';
  reverse?: boolean;
}

interface InterestItem {
  id: string;
  name: string;
  category: string;
  icon: string;
}

interface PersonalityResult {
  extraversion: number;
  openness: number;
  conscientiousness: number;
  agreeableness: number;
  neuroticism: number;
  overallProfile: string;
  strengths: string[];
  growthAreas: string[];
  communicationStyle: string[];
  relationshipTendencies: string[];
  interests: Array<{id: string; name: string; category: string; intensity: number}>;
  lifeGoals: string[];
  communicationPreferences: string[];
  passions: string[];
  hobbies: string[];
}

interface PersonalityTestProps {
  onComplete: (result: PersonalityResult) => void;
  onClose?: () => void;
}

const PERSONALITY_QUESTIONS: PersonalityQuestion[] = [
  { id: 'ext1', text: 'I thrive in social situations and feel energized by being around people', description: 'Think about how you feel at parties, networking events, or group activities', category: 'extraversion' },
  { id: 'ext2', text: 'I prefer intimate one-on-one conversations over large group discussions', description: 'Consider your comfort level in different social settings', category: 'extraversion', reverse: true },
  { id: 'ext3', text: 'I naturally take charge and lead conversations or activities', description: 'Think about your tendency to initiate or guide group situations', category: 'extraversion' },
  { id: 'ext4', text: 'I need quiet time alone to recharge after social interactions', description: 'Consider how you recover from social activities', category: 'extraversion', reverse: true },
  { id: 'open1', text: 'I actively seek out new experiences and adventures', description: 'Think about your approach to trying new things, places, or activities', category: 'openness' },
  { id: 'open2', text: 'I enjoy philosophical discussions and exploring abstract concepts', description: 'Consider your interest in deep, theoretical conversations', category: 'openness' },
  { id: 'open3', text: 'I prefer familiar routines and predictable environments', description: 'Think about your comfort with routine vs. change', category: 'openness', reverse: true },
  { id: 'open4', text: 'I appreciate art, music, and creative expressions in all forms', description: 'Consider your connection to artistic and creative works', category: 'openness' },
  { id: 'cons1', text: 'I meticulously plan my tasks and always meet deadlines', description: 'Think about your approach to work and time management', category: 'conscientiousness' },
  { id: 'cons2', text: 'I keep my living and working spaces organized and tidy', description: 'Consider the state of your personal spaces', category: 'conscientiousness' },
  { id: 'cons3', text: 'I sometimes leave tasks until the last minute', description: 'Think honestly about your procrastination habits', category: 'conscientiousness', reverse: true },
  { id: 'cons4', text: 'I pay careful attention to details and accuracy in everything I do', description: 'Consider your approach to quality and precision', category: 'conscientiousness' },
  { id: 'agree1', text: 'I genuinely care about others\' feelings and put their needs first', description: 'Think about how you prioritize others vs. yourself', category: 'agreeableness' },
  { id: 'agree2', text: 'I find it easy to trust new people and give them the benefit of the doubt', description: 'Consider your natural inclination toward trusting others', category: 'agreeableness' },
  { id: 'agree3', text: 'I avoid confrontation and prefer to keep the peace', description: 'Think about how you handle disagreements or conflicts', category: 'agreeableness' },
  { id: 'agree4', text: 'I actively look for ways to help others solve their problems', description: 'Consider your tendency to support and assist others', category: 'agreeableness' },
  { id: 'neuro1', text: 'I often worry about things that might go wrong in the future', description: 'Think about your tendency to anticipate problems or stress', category: 'neuroticism' },
  { id: 'neuro2', text: 'I remain calm and composed even under significant pressure', description: 'Consider how you handle stressful or challenging situations', category: 'neuroticism', reverse: true },
  { id: 'neuro3', text: 'My emotions can shift quickly throughout the day', description: 'Think about the stability of your mood and feelings', category: 'neuroticism' },
  { id: 'neuro4', text: 'I rarely feel anxious or overwhelmed by daily life', description: 'Consider your general level of anxiety and stress', category: 'neuroticism', reverse: true },
];

const INTEREST_CATEGORIES: Record<string, InterestItem[]> = {
  'Creative & Arts': [
    { id: 'music', name: 'Music', category: 'Creative & Arts', icon: '🎵' },
    { id: 'painting', name: 'Painting & Drawing', category: 'Creative & Arts', icon: '🎨' },
    { id: 'writing', name: 'Creative Writing', category: 'Creative & Arts', icon: '✍️' },
    { id: 'photography', name: 'Photography', category: 'Creative & Arts', icon: '📸' },
    { id: 'dance', name: 'Dancing', category: 'Creative & Arts', icon: '💃' },
    { id: 'theater', name: 'Theater & Acting', category: 'Creative & Arts', icon: '🎭' },
  ],
  'Active & Sports': [
    { id: 'fitness', name: 'Fitness & Gym', category: 'Active & Sports', icon: '💪' },
    { id: 'running', name: 'Running', category: 'Active & Sports', icon: '🏃' },
    { id: 'yoga', name: 'Yoga & Meditation', category: 'Active & Sports', icon: '🧘' },
    { id: 'hiking', name: 'Hiking & Nature', category: 'Active & Sports', icon: '🥾' },
    { id: 'sports', name: 'Team Sports', category: 'Active & Sports', icon: '⚽' },
    { id: 'cycling', name: 'Cycling', category: 'Active & Sports', icon: '🚴' },
  ],
  'Learning & Growth': [
    { id: 'reading', name: 'Reading', category: 'Learning & Growth', icon: '📚' },
    { id: 'languages', name: 'Learning Languages', category: 'Learning & Growth', icon: '🗣️' },
    { id: 'technology', name: 'Technology & Coding', category: 'Learning & Growth', icon: '💻' },
    { id: 'science', name: 'Science & Research', category: 'Learning & Growth', icon: '🔬' },
    { id: 'podcasts', name: 'Podcasts & Documentaries', category: 'Learning & Growth', icon: '🎧' },
    { id: 'philosophy', name: 'Philosophy & Psychology', category: 'Learning & Growth', icon: '🤔' },
  ],
  'Social & Connection': [
    { id: 'cooking', name: 'Cooking & Baking', category: 'Social & Connection', icon: '👨‍🍳' },
    { id: 'travel', name: 'Travel & Adventure', category: 'Social & Connection', icon: '✈️' },
    { id: 'volunteering', name: 'Volunteering', category: 'Social & Connection', icon: '❤️' },
    { id: 'gaming', name: 'Gaming', category: 'Social & Connection', icon: '🎮' },
    { id: 'socializing', name: 'Social Events', category: 'Social & Connection', icon: '🎉' },
    { id: 'gardening', name: 'Gardening', category: 'Social & Connection', icon: '🌱' },
  ]
};

const LIFE_GOAL_OPTIONS = [
  'Build meaningful relationships',
  'Achieve financial independence',
  'Make a positive impact on the world',
  'Pursue creative expression',
  'Maintain health and wellness',
  'Travel and explore the world',
  'Continuous learning and growth',
  'Start a family',
  'Build a successful career',
  'Find inner peace and happiness',
  'Help others achieve their goals',
  'Create something lasting and meaningful'
];

const COMMUNICATION_OPTIONS = [
  'Deep, philosophical conversations',
  'Light-hearted and humorous exchanges',
  'Direct and honest communication',
  'Empathetic and supportive dialogue',
  'Intellectual debates and discussions',
  'Sharing personal stories and experiences',
  'Problem-solving together',
  'Creative brainstorming sessions'
];

const PASSION_OPTIONS = [
  'Art & Creativity',
  'Helping Others',
  'Learning & Growth',
  'Adventure & Travel',
  'Health & Wellness',
  'Technology & Innovation',
  'Leadership & Influence',
  'Nature & Environment',
  'Building & Making',
  'Spirituality & Mindfulness',
];

const HOBBY_OPTIONS = [
  'Music',
  'Sports',
  'Cooking',
  'Gardening',
  'Gaming',
  'Crafts',
  'Photography',
  'Writing',
  'Dancing',
  'Travel',
];

const STEPS = [
  'personality',
  'interests',
  'goals',
  'communication',
  'passions',
  'hobbies',
  'review',
];

type Step = typeof STEPS[number];

const PersonalityTest: React.FC<PersonalityTestProps> = ({ onComplete, onClose }) => {
  // Allow users to retake the test even if previously completed
  // Removed automatic closure check to enable retaking from settings

  const [step, setStep] = useState<Step>('personality');
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, number>>({});
  const [selectedInterests, setSelectedInterests] = useState<Record<string, number>>({});
  const [lifeGoals, setLifeGoals] = useState<string[]>([]);
  const [communicationPrefs, setCommunicationPrefs] = useState<string[]>([]);
  const [passions, setPassions] = useState<string[]>([]);
  const [hobbies, setHobbies] = useState<string[]>([]);
  const [isComplete, setIsComplete] = useState(false);

  // --- Step Navigation ---
  const goNext = () => {
    if (step === 'personality' && currentQuestionIndex < PERSONALITY_QUESTIONS.length - 1) {
      setCurrentQuestionIndex(i => i + 1);
    } else if (step === 'personality' && currentQuestionIndex === PERSONALITY_QUESTIONS.length - 1) {
      // Finished last personality question, move to next step
      const idx = STEPS.indexOf(step);
      setStep(STEPS[idx + 1] as Step);
    } else {
      const idx = STEPS.indexOf(step);
      if (idx < STEPS.length - 1) {
        setStep(STEPS[idx + 1] as Step);
      } else {
        // Review step: submit
        const result = calculatePersonalityResult();
        setIsComplete(true);
        localStorage.setItem('personalityAssessmentComplete', 'true');
        if (onComplete) onComplete(result);
        // Optionally, immediately close the modal after completion
        if (onClose) onClose();
      }
    }
  };
  const goBack = () => {
    const idx = STEPS.indexOf(step);
    if (step === 'personality' && currentQuestionIndex > 0) {
      setCurrentQuestionIndex(i => i - 1);
    } else if (idx > 0) {
      setStep(STEPS[idx - 1] as Step);
      // Only set currentQuestionIndex if returning to personality step
      if (STEPS[idx - 1] === 'personality') setCurrentQuestionIndex(PERSONALITY_QUESTIONS.length - 1);
    }
  };

  // --- Handlers ---
  const handleInterestSelect = (interestId: string, intensity: number) => {
    setSelectedInterests(prev => ({ ...prev, [interestId]: intensity }));
  };
  const handleLifeGoalToggle = (goal: string) => {
    setLifeGoals(prev => prev.includes(goal) ? prev.filter(g => g !== goal) : [...prev, goal]);
  };
  const handleCommunicationPrefToggle = (pref: string) => {
    setCommunicationPrefs(prev => prev.includes(pref) ? prev.filter(p => p !== pref) : [...prev, pref]);
  };
  const handlePassionToggle = (passion: string) => {
    setPassions(prev => prev.includes(passion) ? prev.filter(p => p !== passion) : [...prev, passion]);
  };
  const handleHobbyToggle = (hobby: string) => {
    setHobbies(prev => prev.includes(hobby) ? prev.filter(h => h !== hobby) : [...prev, hobby]);
  };

  // --- Result Calculation ---
  const calculatePersonalityResult = (): PersonalityResult => {
    const getQuestionScore = (questionId: string) => {
      const question = PERSONALITY_QUESTIONS.find(q => q.id === questionId);
      const rawScore = answers[questionId] || 3;
      return question?.reverse ? (6 - rawScore) : rawScore;
    };
    const categories = {
      extraversion: ['ext1', 'ext2', 'ext3', 'ext4'].map(getQuestionScore),
      openness: ['open1', 'open2', 'open3', 'open4'].map(getQuestionScore),
      conscientiousness: ['cons1', 'cons2', 'cons3', 'cons4'].map(getQuestionScore),
      agreeableness: ['agree1', 'agree2', 'agree3', 'agree4'].map(getQuestionScore),
      neuroticism: ['neuro1', 'neuro2', 'neuro3', 'neuro4'].map(getQuestionScore),
    };
    const scores = {
      extraversion: Math.round((categories.extraversion.reduce((a, b) => a + b, 0) / categories.extraversion.length) * 20),
      openness: Math.round((categories.openness.reduce((a, b) => a + b, 0) / categories.openness.length) * 20),
      conscientiousness: Math.round((categories.conscientiousness.reduce((a, b) => a + b, 0) / categories.conscientiousness.length) * 20),
      agreeableness: Math.round((categories.agreeableness.reduce((a, b) => a + b, 0) / categories.agreeableness.length) * 20),
      neuroticism: Math.round((categories.neuroticism.reduce((a, b) => a + b, 0) / categories.neuroticism.length) * 20),
    };
    const processedInterests = Object.entries(selectedInterests).map(([interestId, intensity]) => {
      const allInterests = Object.values(INTEREST_CATEGORIES).flat();
      const interest = allInterests.find(i => i.id === interestId);
      return {
        id: interestId,
        name: interest?.name || interestId,
        category: interest?.category || 'Other',
        intensity,
      };
    });
    // Insights
    const insights = generateInsights(scores);
    const overallProfile = `You have a ${scores.extraversion > 60 ? 'socially engaged' : 'introspective'} personality with ${scores.openness > 60 ? 'high creativity' : 'practical focus'}. You approach life with ${scores.conscientiousness > 60 ? 'strong organization' : 'flexible adaptability'} and ${scores.agreeableness > 60 ? 'genuine care for others' : 'independent thinking'}. Your emotional style is ${scores.neuroticism < 40 ? 'naturally stable' : 'emotionally aware'}.`;
    return {
      ...scores,
      overallProfile,
      strengths: insights.strengths,
      growthAreas: insights.growthAreas,
      communicationStyle: insights.communicationStyle,
      relationshipTendencies: insights.relationshipTendencies,
      interests: processedInterests,
      lifeGoals,
      communicationPreferences: communicationPrefs,
      passions,
      hobbies,
    };
  };
  function generateInsights(scores: Record<string, number>) {
    const insights = {
      strengths: [] as string[],
      growthAreas: [] as string[],
      communicationStyle: [] as string[],
      relationshipTendencies: [] as string[],
    };
    if (scores.extraversion > 70) {
      insights.strengths.push('Natural leader in social situations');
      insights.communicationStyle.push('Outgoing and energetic communicator');
      insights.relationshipTendencies.push('Enjoys active social relationships');
    } else if (scores.extraversion < 40) {
      insights.strengths.push('Deep, thoughtful connections');
      insights.communicationStyle.push('Prefers meaningful one-on-one conversations');
      insights.relationshipTendencies.push('Values intimate, close relationships');
    }
    if (scores.openness > 70) {
      insights.strengths.push('Creative and innovative thinking');
      insights.communicationStyle.push('Enjoys exploring new ideas');
      insights.relationshipTendencies.push('Seeks growth-oriented partnerships');
    } else if (scores.openness < 40) {
      insights.strengths.push('Practical and reliable approach');
      insights.communicationStyle.push('Direct and straightforward communication');
      insights.relationshipTendencies.push('Values stability and consistency');
    }
    if (scores.conscientiousness > 70) {
      insights.strengths.push('Highly organized and dependable');
      insights.communicationStyle.push('Clear and structured communication');
      insights.relationshipTendencies.push('Committed and loyal partner');
    } else if (scores.conscientiousness < 40) {
      insights.growthAreas.push('Building consistent routines');
      insights.communicationStyle.push('Spontaneous and flexible communication');
      insights.relationshipTendencies.push('Prefers adaptable relationship dynamics');
    }
    if (scores.agreeableness > 70) {
      insights.strengths.push('Naturally empathetic and supportive');
      insights.communicationStyle.push('Compassionate and understanding');
      insights.relationshipTendencies.push('Highly cooperative and caring');
    } else if (scores.agreeableness < 40) {
      insights.strengths.push('Independent and honest');
      insights.communicationStyle.push('Direct and authentic communication');
      insights.relationshipTendencies.push('Values independence in relationships');
    }
    if (scores.neuroticism > 70) {
      insights.growthAreas.push('Managing stress and emotional regulation');
      insights.communicationStyle.push('Emotionally expressive');
      insights.relationshipTendencies.push('Needs emotional support and understanding');
    } else if (scores.neuroticism < 40) {
      insights.strengths.push('Emotionally stable and resilient');
      insights.communicationStyle.push('Calm and composed communication');
      insights.relationshipTendencies.push('Provides emotional stability');
    }
    return insights;
  }

  // --- Step Content Renderers ---
  function renderPersonalityStep() {
    const q = PERSONALITY_QUESTIONS[currentQuestionIndex];
    const progress = ((currentQuestionIndex + 1) / PERSONALITY_QUESTIONS.length) * 100;
    return (
      <div>
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">
              Question {currentQuestionIndex + 1} of {PERSONALITY_QUESTIONS.length}
            </span>
            <span className="text-sm text-gray-500">{Math.round(progress)}% complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
        <div className="p-8">
          <h3 className="text-xl font-semibold text-gray-800 mb-2">{q.text}</h3>
          {q.description && <p className="text-gray-500 mb-6">{q.description}</p>}
          <div className="space-y-3">
            {[1,2,3,4,5].map(val => (
              <button
                key={val}
                onClick={() => setAnswers(prev => ({ ...prev, [q.id]: val }))}
                className={`w-full flex items-center px-4 py-3 rounded-lg border-2 transition text-left mb-2 ${
                  answers[q.id] === val
                    ? 'bg-purple-100 border-purple-500 text-purple-800 font-semibold'
                    : 'bg-white border-gray-200 text-gray-700 hover:border-purple-300'
                }`}
                aria-label={`Select ${val} for ${q.text}`}
              >
                <span className="mr-3">
                  {val === 1 && 'Strongly Disagree'}
                  {val === 2 && 'Disagree'}
                  {val === 3 && 'Neutral'}
                  {val === 4 && 'Agree'}
                  {val === 5 && 'Strongly Agree'}
                </span>
                {answers[q.id] === val && <CheckCircle className="w-5 h-5 text-purple-600 ml-auto" />}
              </button>
            ))}
          </div>
        </div>
      </div>
    );
  }
  function renderInterestsStep() {
    return (
      <div className="p-8">
        <h3 className="text-xl font-semibold text-gray-800 mb-2 flex items-center"><Star className="w-6 h-6 mr-2 text-yellow-500" />Select Your Interests</h3>
        <p className="text-gray-500 mb-6">Choose interests and rate your passion for each (1-5 stars).</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Object.entries(INTEREST_CATEGORIES).map(([cat, items]) => (
            <div key={cat}>
              <h4 className="font-bold text-purple-700 mb-2">{cat}</h4>
              <div className="flex flex-wrap gap-2">
                {items.map(item => (
                  <div key={item.id} className="flex items-center gap-1 bg-gray-100 rounded-lg px-2 py-1">
                    <span className="text-xl">{item.icon}</span>
                    <span>{item.name}</span>
                    <div className="flex ml-2">
                      {[1,2,3,4,5].map(val => (
                        <button
                          key={val}
                          onClick={() => handleInterestSelect(item.id, val)}
                          className={`mx-0.5 ${selectedInterests[item.id] === val ? 'text-yellow-500' : 'text-gray-300'}`}
                          aria-label={`Set passion for ${item.name} to ${val}`}
                        >
                          <Star fill={selectedInterests[item.id] && selectedInterests[item.id] >= val ? '#facc15' : 'none'} className="w-4 h-4" />
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
  function renderGoalsStep() {
    return (
      <div className="p-8">
        <h3 className="text-xl font-semibold text-gray-800 mb-2 flex items-center"><Target className="w-6 h-6 mr-2 text-blue-500" />Select Your Life Goals</h3>
        <p className="text-gray-500 mb-6">Pick all that resonate with you.</p>
        <div className="flex flex-wrap gap-2">
          {LIFE_GOAL_OPTIONS.map(goal => (
            <button
              key={goal}
              onClick={() => handleLifeGoalToggle(goal)}
              className={`px-4 py-2 rounded-lg border-2 transition ${lifeGoals.includes(goal) ? 'bg-blue-100 border-blue-500 text-blue-800' : 'bg-white border-gray-200 text-gray-700 hover:border-blue-300'}`}
            >
              {goal}
            </button>
          ))}
        </div>
      </div>
    );
  }
  function renderCommunicationStep() {
    return (
      <div className="p-8">
        <h3 className="text-xl font-semibold text-gray-800 mb-2 flex items-center"><MessageCircle className="w-6 h-6 mr-2 text-green-500" />Communication Preferences</h3>
        <p className="text-gray-500 mb-6">Select the styles you most enjoy.</p>
        <div className="flex flex-wrap gap-2">
          {COMMUNICATION_OPTIONS.map(opt => (
            <button
              key={opt}
              onClick={() => handleCommunicationPrefToggle(opt)}
              className={`px-4 py-2 rounded-lg border-2 transition ${communicationPrefs.includes(opt) ? 'bg-green-100 border-green-500 text-green-800' : 'bg-white border-gray-200 text-gray-700 hover:border-green-300'}`}
            >
              {opt}
            </button>
          ))}
        </div>
      </div>
    );
  }
  function renderPassionsStep() {
    return (
      <div className="p-8">
        <h3 className="text-xl font-semibold text-gray-800 mb-2 flex items-center"><Heart className="w-6 h-6 mr-2 text-pink-500" />What Are You Passionate About?</h3>
        <p className="text-gray-500 mb-6">Pick your top passions.</p>
        <div className="flex flex-wrap gap-2">
          {PASSION_OPTIONS.map(passion => (
            <button
              key={passion}
              onClick={() => handlePassionToggle(passion)}
              className={`px-4 py-2 rounded-lg border-2 transition ${passions.includes(passion) ? 'bg-pink-100 border-pink-500 text-pink-800' : 'bg-white border-gray-200 text-gray-700 hover:border-pink-300'}`}
            >
              {passion}
            </button>
          ))}
        </div>
      </div>
    );
  }
  function renderHobbiesStep() {
    return (
      <div className="p-8">
        <h3 className="text-xl font-semibold text-gray-800 mb-2 flex items-center"><Zap className="w-6 h-6 mr-2 text-yellow-600" />Your Hobbies</h3>
        <p className="text-gray-500 mb-6">Select your favorite hobbies.</p>
        <div className="flex flex-wrap gap-2">
          {HOBBY_OPTIONS.map(hobby => (
            <button
              key={hobby}
              onClick={() => handleHobbyToggle(hobby)}
              className={`px-4 py-2 rounded-lg border-2 transition ${hobbies.includes(hobby) ? 'bg-yellow-100 border-yellow-500 text-yellow-800' : 'bg-white border-gray-200 text-gray-700 hover:border-yellow-300'}`}
            >
              {hobby}
            </button>
          ))}
        </div>
      </div>
    );
  }
  function renderReviewStep() {
    const result = calculatePersonalityResult();
    return (
      <div className="p-8">
        <h3 className="text-2xl font-bold text-gray-800 mb-4 flex items-center"><CheckCircle className="w-7 h-7 mr-2 text-green-600" />Review & Submit</h3>
        <p className="mb-4 text-gray-600">Here's a summary of your digital twin profile. You can go back to edit any section.</p>
        <div className="mb-4">
          <div className="font-semibold text-purple-700 mb-1">Personality Scores</div>
          <div className="flex flex-wrap gap-4">
            <span>Extraversion: <b>{result.extraversion}</b></span>
            <span>Openness: <b>{result.openness}</b></span>
            <span>Conscientiousness: <b>{result.conscientiousness}</b></span>
            <span>Agreeableness: <b>{result.agreeableness}</b></span>
            <span>Neuroticism: <b>{result.neuroticism}</b></span>
          </div>
        </div>
        <div className="mb-2"><span className="font-semibold text-purple-700">Profile:</span> {result.overallProfile}</div>
        <div className="mb-2"><span className="font-semibold text-purple-700">Strengths:</span> {result.strengths.join(', ')}</div>
        <div className="mb-2"><span className="font-semibold text-purple-700">Growth Areas:</span> {result.growthAreas.join(', ')}</div>
        <div className="mb-2"><span className="font-semibold text-purple-700">Communication Style:</span> {result.communicationStyle.join(', ')}</div>
        <div className="mb-2"><span className="font-semibold text-purple-700">Relationship Tendencies:</span> {result.relationshipTendencies.join(', ')}</div>
        <div className="mb-2"><span className="font-semibold text-purple-700">Interests:</span> {result.interests.map(i => `${i.name} (${i.intensity}/5)`).join(', ')}</div>
        <div className="mb-2"><span className="font-semibold text-purple-700">Life Goals:</span> {result.lifeGoals.join(', ')}</div>
        <div className="mb-2"><span className="font-semibold text-purple-700">Communication Preferences:</span> {result.communicationPreferences.join(', ')}</div>
        <div className="mb-2"><span className="font-semibold text-purple-700">Passions:</span> {result.passions.join(', ')}</div>
        <div className="mb-2"><span className="font-semibold text-purple-700">Hobbies:</span> {result.hobbies.join(', ')}</div>
      </div>
    );
  }

  // --- Main Render ---
  if (isComplete) {
    // Optionally, auto-close after a short delay
    setTimeout(() => {
      if (onClose) onClose();
    }, 1000);
    return (
      <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Personality Assessment Complete!</h2>
          <p className="text-gray-600">Your results have been saved to enhance your digital twin's understanding of your personality.</p>
        </div>
        {onClose && (
          <div className="text-center">
            <button
              onClick={onClose}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition duration-200"
            >
              Continue
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="bg-gradient-to-r from-purple-600 to-pink-500 p-6">
        <div className="flex items-center text-white">
          <Brain className="w-8 h-8 mr-3" />
          <div>
            <h1 className="text-2xl font-bold">Personality Assessment</h1>
            <p className="text-purple-100">Helping your digital twin understand you better</p>
          </div>
        </div>
      </div>
      {/* Step Content */}
      {step === 'personality' && renderPersonalityStep()}
      {step === 'interests' && renderInterestsStep()}
      {step === 'goals' && renderGoalsStep()}
      {step === 'communication' && renderCommunicationStep()}
      {step === 'passions' && renderPassionsStep()}
      {step === 'hobbies' && renderHobbiesStep()}
      {step === 'review' && renderReviewStep()}
      {/* Navigation */}
      <div className="p-6 bg-gray-50 flex justify-between">
        <button
          onClick={goBack}
          disabled={step === 'personality' && currentQuestionIndex === 0}
          className={`flex items-center px-6 py-3 rounded-lg font-medium transition duration-200 ${
            (step === 'personality' && currentQuestionIndex === 0)
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-gray-700 hover:bg-gray-200'
          }`}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Previous
        </button>
        <button
          onClick={goNext}
          disabled={
            (step === 'personality' && !answers[PERSONALITY_QUESTIONS[currentQuestionIndex].id]) ||
            (step === 'interests' && Object.keys(selectedInterests).length === 0) ||
            (step === 'goals' && lifeGoals.length === 0) ||
            (step === 'communication' && communicationPrefs.length === 0) ||
            (step === 'passions' && passions.length === 0) ||
            (step === 'hobbies' && hobbies.length === 0)
          }
          className={`flex items-center px-6 py-3 rounded-lg font-medium transition duration-200 ${
            ((step === 'personality' && !answers[PERSONALITY_QUESTIONS[currentQuestionIndex].id]) ||
            (step === 'interests' && Object.keys(selectedInterests).length === 0) ||
            (step === 'goals' && lifeGoals.length === 0) ||
            (step === 'communication' && communicationPrefs.length === 0) ||
            (step === 'passions' && passions.length === 0) ||
            (step === 'hobbies' && hobbies.length === 0))
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-purple-600 text-white hover:bg-purple-700'
          }`}
        >
          {step === 'review' ? 'Submit' : 'Next'}
          <ArrowRight className="w-4 h-4 ml-2" />
        </button>
      </div>
    </div>
  );
};

export default PersonalityTest;
