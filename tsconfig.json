{
  "compilerOptions": {
    // Target modern browsers
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    
    // Enable JSX for React
    "jsx": "react-jsx",
    
    // Module settings
    "module": "ESNext",
    "moduleResolution": "bundler",
    
    // Allow default imports from modules with no default export
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    
    // Bundler mode settings (for Vite)
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    
    // Type checking
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true,
    
    // Path mappings (if you use them)
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    
    // Include type definitions
    "types": ["vite/client", "node"]
  },
  "include": [
    "src/**/*",
    "src/**/*.tsx",
    "src/**/*.ts",
    "vite-env.d.ts",
    "vite.config.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build"
  ]
}