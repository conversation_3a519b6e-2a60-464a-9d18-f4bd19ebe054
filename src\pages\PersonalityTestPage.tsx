import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import PersonalityTest from '../components/personality/PersonalityTest';
import { useAuth } from '../hooks/useAuth';
import { useToast } from '../hooks/useToast';
import { useProfile } from '../context/ProfileContext';
import { supabase } from '../lib/supabase';

interface PersonalityResult {
  extraversion: number;
  openness: number;
  conscientiousness: number;
  agreeableness: number;
  neuroticism: number;
  overallProfile: string;
  strengths: string[];
  growthAreas: string[];
  communicationStyle: string[];
  relationshipTendencies: string[];
}

const PersonalityTestPage: React.FC = () => {
  const { user } = useAuth();
  const { refreshProfile } = useProfile();
  const navigate = useNavigate();
  const location = useLocation();
  const { showSuccess, showError, showLoading, dismiss } = useToast();
  const [isSaving, setIsSaving] = useState(false);

  // Check if user was redirected here due to mandatory test requirement
  const isRedirectedFromGuard = location.state?.from && location.state?.message;
  const redirectMessage = location.state?.message;

  const handleTestComplete = async (result: PersonalityResult) => {
    if (!user?.id) {
      showError('You must be logged in to save your personality test results.');
      return;
    }

    setIsSaving(true);
    const loadingToast = showLoading('Saving your personality assessment...');    try {
      // Prepare personality insights data structure
      const personalityInsights = {
        overallProfile: result.overallProfile,
        strengths: result.strengths,
        growthAreas: result.growthAreas,
        communicationStyle: result.communicationStyle,
        relationshipTendencies: result.relationshipTendencies,
        testDate: new Date().toISOString(),
        bigFiveScores: {
          extraversion: result.extraversion,
          openness: result.openness,
          conscientiousness: result.conscientiousness,
          agreeableness: result.agreeableness,
          neuroticism: result.neuroticism
        }
      };

      // Save personality test results to the user's profile
      const personalityData = {
        extraversion_score: result.extraversion,
        openness_score: result.openness,
        conscientiousness_score: result.conscientiousness,
        agreeableness_score: result.agreeableness,
        neuroticism_score: result.neuroticism,
        personality_insights: personalityInsights,
        personality_test_completed_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('profiles')
        .update(personalityData)
        .eq('id', user.id);

      if (error) throw error;

      // Refresh the profile to get the updated personality data
      await refreshProfile();

      dismiss(loadingToast);
      showSuccess('🎉 Personality test completed! Your digital twin is now ready to make meaningful connections.');
      localStorage.setItem('personalityAssessmentComplete', 'true');
      
      // Navigate back to the page they came from or dashboard
      // If coming from settings, return to settings; otherwise go to dashboard
      setTimeout(() => {
        const returnTo = location.state?.from?.pathname || '/dashboard';
        navigate(returnTo);
      }, 2000);

    } catch (error) {
      console.error('Error saving personality test results:', error);
      dismiss(loadingToast);
      showError('Failed to save your personality test results. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    navigate(-1); // Go back to previous page
  };
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {isRedirectedFromGuard && (
          <div className="max-w-4xl mx-auto mb-6">
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-purple-800">
                    Personality Assessment Required
                  </h3>
                  <div className="mt-2 text-sm text-purple-700">
                    <p>{redirectMessage}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        <PersonalityTest 
          onComplete={handleTestComplete}
          onClose={isSaving ? undefined : handleClose}
        />
      </div>
    </div>
  );
};

export default PersonalityTestPage;
