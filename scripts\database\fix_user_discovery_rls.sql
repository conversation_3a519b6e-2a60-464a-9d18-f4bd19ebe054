-- Fix user discovery by adding test users directly via SQL Editor
-- This bypasses RLS restrictions and ensures users exist for discovery testing

-- First, let's check what RLS policies exist
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'profiles';

-- Insert your actual profiles (these should match your debug info)
INSERT INTO public.profiles (
  id, 
  username, 
  full_name, 
  age, 
  gender, 
  bio,
  created_at,
  updated_at
) VALUES 
  -- Your actual <NAME_EMAIL>
  ('1818c8d3-c9a1-4a53-9b01-fc51e45ce55d', 'roberto.coscia.it', '<PERSON>', 30, 'male', 'Original <PERSON> from Italy. Tech enthusiast and entrepreneur.', '2025-05-29T16:52:49Z', NOW()),
  
  -- The other <PERSON> from <EMAIL>  
  ('c6f6f365-6150-4bf2-96ee-39be2ec41094', 'robert<PERSON>_co<PERSON><PERSON>', '<PERSON>', 28, 'male', '<PERSON> from <EMAIL>. Love technology and innovation.', '2025-05-10T19:07:10Z', NOW()),
  
  -- Additional test users for discovery testing
  ('550e8400-e29b-41d4-a716-446655440001', 'roberto_martinez', '<PERSON> <PERSON>', 29, 'male', '<PERSON> developer from Spain. <PERSON> technology and meeting new people.', NOW(), NOW()),
  
  ('550e8400-e29b-41d4-a716-446655440002', 'roberto_silva', '<PERSON> <PERSON>', 27, 'male', 'Designer and entrepreneur from Brazil. Passionate about creativity.', NOW(), NOW()),
  
  ('550e8400-e29b-41d4-a716-446655440003', 'alice_johnson', 'Alice Johnson', 28, 'female', 'Love hiking, reading, and good coffee. Looking for meaningful connections.', NOW(), NOW()),
  
  ('550e8400-e29b-41d4-a716-446655440004', 'bob_smith', 'Bob Smith', 32, 'male', 'Software engineer by day, chef by night. Enjoy traveling and trying new cuisines.', NOW(), NOW()),
  
  ('550e8400-e29b-41d4-a716-446655440005', 'carol_davis', 'Carol Davis', 26, 'female', 'Artist and yoga instructor. Passionate about creativity and mindfulness.', NOW(), NOW())

ON CONFLICT (id) DO UPDATE SET
  username = EXCLUDED.username,
  full_name = EXCLUDED.full_name,
  age = EXCLUDED.age,
  gender = EXCLUDED.gender,
  bio = EXCLUDED.bio,
  updated_at = NOW();

-- Verify the insert worked
SELECT 'INSERTED PROFILES:' as status;
SELECT id, username, full_name, age, gender, created_at 
FROM public.profiles 
ORDER BY created_at DESC;

-- Test a search query (should find Roberto users)
SELECT 'ROBERTO SEARCH TEST:' as status;
SELECT id, username, full_name, age, gender 
FROM public.profiles 
WHERE full_name ILIKE '%roberto%' OR username ILIKE '%roberto%'
ORDER BY full_name;

-- Test browse profiles logic (exclude specific user)
SELECT 'BROWSE PROFILES TEST (excluding first Roberto):' as status;
SELECT id, username, full_name, age, gender 
FROM public.profiles 
WHERE id != '1818c8d3-c9a1-4a53-9b01-fc51e45ce55d'
ORDER BY created_at DESC;

-- Count totals
SELECT 'TOTAL COUNTS:' as status;
SELECT 
  COUNT(*) as total_profiles,
  COUNT(*) FILTER (WHERE full_name ILIKE '%roberto%') as roberto_profiles,
  COUNT(*) FILTER (WHERE id != '1818c8d3-c9a1-4a53-9b01-fc51e45ce55d') as other_profiles
FROM public.profiles;
