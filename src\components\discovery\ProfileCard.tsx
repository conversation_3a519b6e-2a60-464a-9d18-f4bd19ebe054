import React from 'react';
import { UserCircle } from 'lucide-react';
import { Profile } from '../../types';
import UserBadges from '../tagging/UserBadges';

/**
 * @file ProfileCard.tsx
 * @description Component to display a summary of a user's profile in a card format.
 * Used in discovery sections to provide a quick overview of users.
 */

interface ProfileCardProps {
  /** The profile data to display on the card. */
  profile: Profile;
  /** Optional click handler for when the card is clicked. */
  onClick?: () => void;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ profile, onClick }) => {
  // // Function to get initials for placeholder avatar - Currently UserCircle icon is used.
  // const getInitials = (name: string): string => {
  //   if (!name) return '?';
  //   const names = name.split(' ');
  //   const initials = names.map(n => n[0]).join('');
  //   return initials.length > 2 ? initials.substring(0, 2) : initials;
  // };

  /**
   * Truncates a string (bio) to a specified maximum length, appending "..." if truncated.
   * @param bio The biography string to truncate.
   * @param maxLength The maximum length before truncation. Defaults to 100.
   * @returns The truncated bio string or a default message if no bio is available.
   */
  const truncateBio = (bio: string | undefined, maxLength: number = 100): string => {
    if (!bio || bio.trim() === '') return 'No bio available.'; // Handle empty or whitespace-only bios
    if (bio.length <= maxLength) return bio;
    return `${bio.substring(0, maxLength)}...`;
  };

  return (
    <div
      className="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1 cursor-pointer"
      onClick={onClick}
    >
      {/* Avatar Section */}
      <div className="h-40 bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
        {profile.avatar_url ? (
          <img
            src={profile.avatar_url}
            alt={`${profile.name}'s avatar`}
            className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-md"
          />
        ) : (
          <div className="w-24 h-24 rounded-full bg-gray-300 flex items-center justify-center border-4 border-white shadow-md">
            <UserCircle className="w-16 h-16 text-gray-500" />
            {/* Fallback to initials if UserCircle is too generic or not preferred
            <span className="text-3xl font-semibold text-gray-600">{getInitials(profile.name)}</span>
            */}
          </div>
        )}
      </div>

      {/* Content Section */}
      <div className="p-6">
        {/* Name */}
        <h2 className="text-2xl font-bold text-gray-800 mb-1 truncate" title={profile.name}>
          {profile.name || 'Unnamed User'}
        </h2>

        {/* Age & Gender */}
        {(profile.age || profile.gender) && (
          <p className="text-sm text-gray-600 mb-3">
            {profile.age && <span>{profile.age}</span>}
            {profile.age && profile.gender && <span>, </span>}
            {profile.gender && <span>{profile.gender}</span>}
          </p>
        )}

        {/* Bio Snippet */}
        <p className="text-gray-700 text-sm mb-4 h-16 overflow-hidden">
          {truncateBio(profile.bio)}
        </p>

        {/* User Badges */}
        <UserBadges
          userId={profile.id}
          size="sm"
          className="mb-2"
        />

        {/* Interests Tags */}
        {profile.interests && profile.interests.length > 0 && (
          <div className="mb-2">            <h4 className="text-xs font-semibold text-gray-500 uppercase mb-1">Interests</h4>
            <div className="flex flex-wrap gap-2">
              {profile.interests.slice(0, 3).map((interest, index) => (
                <span
                  key={index}
                  className="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-full"
                >
                  {typeof interest === 'string' ? interest : interest.name}
                </span>
              ))}
              {profile.interests.length > 3 && (
                <span className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">
                  +{profile.interests.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}
        
        {/* Placeholder for a "View Profile" button or similar action if needed later */}
        {/* <button className="mt-4 w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            View Profile
          </button> */}
      </div>
    </div>
  );
};

export default ProfileCard;
