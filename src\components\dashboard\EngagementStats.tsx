import React from 'react';
import { BarChart2, MessageSquare, Users, Calendar } from 'lucide-react';
import { Conversation } from '../../types';

interface EngagementStatsProps {
  conversations: Conversation[];
}

export const EngagementStats: React.FC<EngagementStatsProps> = ({ conversations = [] }) => {
  // Calculate total messages
  const totalMessages = conversations.reduce((total, convo) => {
    return total + (convo.messages?.length || 0);
  }, 0);

  // Calculate average messages per conversation
  const averageMessages = conversations.length > 0 
    ? Math.round(totalMessages / conversations.length) 
    : 0;

  // Calculate active days (days with at least one message)
  const activeDays = new Set();
  conversations.forEach(convo => {
    convo.messages?.forEach(message => {
      const date = new Date(message.timestamp).toDateString();
      activeDays.add(date);
    });
  });
  
  return (
    <div className="space-y-5">
      <div className="flex items-center">
        <div className="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center mr-4">
          <MessageSquare className="w-5 h-5 text-purple-600" />
        </div>
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium">Total Messages</div>
          <div className="text-xl font-bold text-gray-800">{totalMessages}</div>
        </div>
      </div>
      
      <div className="flex items-center">
        <div className="w-10 h-10 rounded-lg bg-pink-100 flex items-center justify-center mr-4">
          <Users className="w-5 h-5 text-pink-600" />
        </div>
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium">Active Conversations</div>
          <div className="text-xl font-bold text-gray-800">{conversations.length}</div>
        </div>
      </div>
      
      <div className="flex items-center">
        <div className="w-10 h-10 rounded-lg bg-indigo-100 flex items-center justify-center mr-4">
          <BarChart2 className="w-5 h-5 text-indigo-600" />
        </div>
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium">Avg. Messages/Conversation</div>
          <div className="text-xl font-bold text-gray-800">{averageMessages}</div>
        </div>
      </div>
      
      <div className="flex items-center">
        <div className="w-10 h-10 rounded-lg bg-teal-100 flex items-center justify-center mr-4">
          <Calendar className="w-5 h-5 text-teal-600" />
        </div>
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium">Active Days</div>
          <div className="text-xl font-bold text-gray-800">{activeDays.size}</div>
        </div>
      </div>
      
      <div className="pt-3 mt-4 border-t border-gray-200">
        <div className="text-sm text-gray-500 mb-2">Engagement Progress</div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
          <div 
            className="bg-gradient-to-r from-purple-600 to-pink-500 h-2.5 rounded-full" 
            style={{ width: `${Math.min(conversations.length * 10, 100)}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-500">
          <span>Getting Started</span>
          <span>Active Dater</span>
        </div>
      </div>
    </div>
  );
};