-- Check and create missing tables for remote Supabase database
-- Run this in Supabase SQL Editor to fix 404 errors

-- First, let's check what tables exist
SELECT tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename;

-- Create user_interest_tags table if missing
CREATE TABLE IF NOT EXISTS public.user_interest_tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tagger_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  tagged_user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  tag_type text NOT NULL CHECK (tag_type IN ('interested', 'very_interested', 'potential_match', 'willing_to_contact')),
  emoji text,
  created_at timestamptz DEFAULT now(),
  UNIQUE(tagger_id, tagged_user_id)
);

-- Create contact_willingness_status table if missing
CREATE TABLE IF NOT EXISTS public.contact_willingness_status (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  is_open_to_contact boolean DEFAULT false,
  status_message text,
  updated_at timestamptz DEFAULT now()
);

-- Create user_notifications table if missing
CREATE TABLE IF NOT EXISTS public.user_notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  type text NOT NULL CHECK (type IN ('interest_tag', 'contact_willingness', 'mutual_interest', 'willing_to_contact')),
  title text NOT NULL,
  message text NOT NULL,
  data jsonb,
  read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.user_interest_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_willingness_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for user_interest_tags
CREATE POLICY "Users can view interest tags they created or received" ON public.user_interest_tags
  FOR SELECT USING (auth.uid() = tagger_id OR auth.uid() = tagged_user_id);

CREATE POLICY "Users can create interest tags" ON public.user_interest_tags
  FOR INSERT WITH CHECK (auth.uid() = tagger_id);

CREATE POLICY "Users can update their own interest tags" ON public.user_interest_tags
  FOR UPDATE USING (auth.uid() = tagger_id);

CREATE POLICY "Users can delete their own interest tags" ON public.user_interest_tags
  FOR DELETE USING (auth.uid() = tagger_id);

-- Add RLS policies for contact_willingness_status
CREATE POLICY "Users can view all contact willingness status" ON public.contact_willingness_status
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own contact willingness" ON public.contact_willingness_status
  FOR ALL USING (auth.uid() = user_id);

-- Add RLS policies for user_notifications
CREATE POLICY "Users can view their own notifications" ON public.user_notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.user_notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- Verify tables were created
SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%user%' ORDER BY tablename;
