import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVnZ2NjYmtoeHBlbXd1dHNxc3BoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc4MDQ5MDgsImV4cCI6MjA1MzM4MDkwOH0.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixTaggingRLS() {
    console.log('🔐 Fixing tagging RLS policies...\n');

    try {
        // Check authentication first
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        
        if (!user) {
            console.log('⚠️ No authenticated user found');
            console.log('🔑 Please log in to the application first');
            return;
        }
        
        console.log(`✅ Authenticated as: ${user.email} (${user.id})`);

        // Test current state
        console.log('\n1️⃣ Testing current tagging functionality...');
        
        // Try to create a test tag
        const { data: testTag, error: tagError } = await supabase
            .from('user_interest_tags')
            .insert({
                tagger_id: user.id,
                tagged_user_id: user.id, // Self-tag for testing
                tag_type: 'interested',
                emoji: '👍'
            })
            .select();
            
        if (tagError) {
            console.error('❌ Tag creation failed:', tagError.message);
            console.log('\n2️⃣ Applying RLS policy fixes...');
            
            // Apply the SQL fixes using individual statements
            const sqlStatements = [
                `DROP POLICY IF EXISTS "Users can view interest tags they created or received" ON public.user_interest_tags`,
                `DROP POLICY IF EXISTS "Users can create interest tags" ON public.user_interest_tags`,
                `DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.user_interest_tags`,
                `DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.user_interest_tags`,
                `CREATE POLICY "Allow authenticated users to read all tags" ON public.user_interest_tags FOR SELECT USING (auth.uid() IS NOT NULL)`,
                `CREATE POLICY "Allow authenticated users to insert tags" ON public.user_interest_tags FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND auth.uid()::text = tagger_id::text)`,
                `CREATE POLICY "Allow users to update their own tags" ON public.user_interest_tags FOR UPDATE USING (auth.uid()::text = tagger_id::text)`,
                `CREATE POLICY "Allow users to delete their own tags" ON public.user_interest_tags FOR DELETE USING (auth.uid()::text = tagger_id::text)`
            ];
            
            // Note: We can't execute DDL statements via the Supabase client
            console.log('⚠️ SQL statements need to be run manually in Supabase dashboard:');
            sqlStatements.forEach((stmt, i) => {
                console.log(`${i + 1}. ${stmt};`);
            });
            
        } else {
            console.log('✅ Tag creation successful:', testTag);
            
            // Clean up test tag
            await supabase
                .from('user_interest_tags')
                .delete()
                .eq('id', testTag[0].id);
                
            console.log('🧹 Test tag cleaned up');
        }

        // Test profile access
        console.log('\n3️⃣ Testing profile access...');
        const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('id, username, full_name')
            .eq('id', user.id)
            .single();
            
        if (profileError) {
            console.error('❌ Profile access failed:', profileError.message);
        } else {
            console.log('✅ Profile access successful:', profile);
        }

        // Test discovery functionality
        console.log('\n4️⃣ Testing discovery...');
        const { data: discoveryProfiles, error: discoveryError } = await supabase
            .from('profiles')
            .select('id, username, full_name, age, gender, bio')
            .not('id', 'eq', user.id) // Exclude current user
            .limit(3);
            
        if (discoveryError) {
            console.error('❌ Discovery failed:', discoveryError.message);
        } else {
            console.log(`✅ Discovery working - found ${discoveryProfiles?.length || 0} other profiles`);
        }

    } catch (error) {
        console.error('❌ Unexpected error:', error);
    }
}

fixTaggingRLS();
