import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { Profile, PersonalityTrait, Interest, RelationshipPreference } from '../types';
import { saveProfile, getProfile } from '../lib/profile';
import { useAuth } from '../components/auth/AuthProvider';

interface ProfileContextType {
  profile: Profile | null;
  isProfileComplete: boolean;
  isLoading: boolean;
  createProfile: (profile: Profile) => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
  addPersonalityTrait: (trait: PersonalityTrait) => Promise<void>;
  removePersonalityTrait: (traitId: string) => Promise<void>;
  addInterest: (interest: Interest) => Promise<void>;
  removeInterest: (interestId: string) => Promise<void>;
  updateRelationshipPreferences: (preferences: RelationshipPreference[]) => Promise<void>;
  setBoundaries: (boundaries: string[]) => Promise<void>;
  refreshProfile: () => Promise<void>;
}

const ProfileContext = createContext<ProfileContextType>({ 
  profile: null,
  isProfileComplete: false,
  isLoading: true,
  createProfile: async () => {},
  updateProfile: async () => {},
  addPersonalityTrait: async () => {},
  removePersonalityTrait: async () => {},
  addInterest: async () => {},
  removeInterest: async () => {},
  updateRelationshipPreferences: async () => {},
  setBoundaries: async () => {},
  refreshProfile: async () => {}
});

export const ProfileProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  const isProfileComplete = Boolean(
    profile && 
    profile.personalityTraits && 
    profile.personalityTraits.length > 0 && 
    profile.interests && 
    profile.interests.length > 0 && 
    profile.relationshipPreferences && 
    profile.relationshipPreferences.length > 0
  );

  // Load profile function
  const loadProfile = async () => {
    if (user?.id) {
      setIsLoading(true);
      try {
        const userProfile = await getProfile(user.id);
        setProfile(userProfile);
      } catch (error) {
        console.error('Error loading profile:', error);
        setProfile(null);
      } finally {
        setIsLoading(false);
      }
    } else {
      // If no user, clear profile but don't set loading to false until we know auth state
      setProfile(null);
      if (user === null) {
        // Only set loading to false if user is explicitly null (not undefined)
        setIsLoading(false);
      }
    }
  };

  // Load profile when user is authenticated
  useEffect(() => {
    loadProfile();
  }, [user]);

  const refreshProfile = async () => {
    if (user?.id) {
      await loadProfile();
    }
  };

  const createProfile = async (newProfile: Profile) => {
    await saveProfile(newProfile);
    setProfile(newProfile);
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!profile) return;

    const updatedProfile = { ...profile, ...updates };
    await saveProfile(updatedProfile);
    setProfile(updatedProfile);
  };

  const addPersonalityTrait = async (trait: PersonalityTrait) => {
    if (!profile) return;

    const updatedProfile = {
      ...profile,
      personalityTraits: [...(profile.personalityTraits || []), trait]
    };
    await saveProfile(updatedProfile);
    setProfile(updatedProfile);
  };

  const removePersonalityTrait = async (traitId: string) => {
    if (!profile || !profile.personalityTraits) return;

    const updatedProfile = {
      ...profile,
      personalityTraits: profile.personalityTraits.filter(trait => trait.id !== traitId)
    };
    await saveProfile(updatedProfile);
    setProfile(updatedProfile);
  };

  const addInterest = async (interest: Interest) => {
    if (!profile) return;

    const updatedProfile = {
      ...profile,
      interests: [...(profile.interests || []), interest]
    };
    await saveProfile(updatedProfile);
    setProfile(updatedProfile);
  };

  const removeInterest = async (interestId: string) => {
    if (!profile || !profile.interests) return;

    const updatedProfile = {
      ...profile,
      interests: profile.interests.filter(interest => interest.id !== interestId)
    };
    await saveProfile(updatedProfile);
    setProfile(updatedProfile);
  };

  const updateRelationshipPreferences = async (preferences: RelationshipPreference[]) => {
    if (!profile) return;

    const updatedProfile = {
      ...profile,
      relationshipPreferences: preferences
    };
    await saveProfile(updatedProfile);
    setProfile(updatedProfile);
  };

  const setBoundaries = async (boundaries: string[]) => {
    if (!profile) return;

    const updatedProfile = {
      ...profile,
      boundaries
    };
    await saveProfile(updatedProfile);
    setProfile(updatedProfile);
  };

  return (
    <ProfileContext.Provider 
      value={{ 
        profile, 
        isProfileComplete, 
        isLoading,
        createProfile, 
        updateProfile,
        addPersonalityTrait,
        removePersonalityTrait,
        addInterest,
        removeInterest,
        updateRelationshipPreferences,
        setBoundaries,
        refreshProfile
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
};

// Custom hook to use the ProfileContext
function useProfile() {
  const context = useContext(ProfileContext);
  return context;
}

export { useProfile };