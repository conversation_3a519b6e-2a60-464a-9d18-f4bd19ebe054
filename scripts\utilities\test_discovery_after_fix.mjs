import { createClient } from '@supabase/supabase-js';

// Use environment variables directly
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://qeukdrgdqlqxrkvlqjjl.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFldWtkcmdkcWxxeHJrdmxxampsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTU1MjU0NDEsImV4cCI6MjAzMTEwMTQ0MX0.HyKGR-5q4Kf4Gl5eKoJcYVVErzq8JnKqiO-kx4Vo-jQ';

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🧪 Testing User Discovery After SQL Fix...');

async function testDiscovery() {
  try {
    // 1. Check total profiles
    console.log('\n1️⃣ Checking total profiles...');
    const { data: allProfiles, error: allError } = await supabase
      .from('profiles')
      .select('id, username, full_name, age, gender');
    
    if (allError) {
      console.log('❌ Error fetching profiles:', allError);
      return;
    }
    
    console.log(`✅ Total profiles found: ${allProfiles?.length || 0}`);
    allProfiles?.forEach(profile => {
      console.log(`   • ${profile.full_name} (@${profile.username}) - ${profile.age}y, ${profile.gender}`);
    });

    // 2. Test search for "roberto"
    console.log('\n2️⃣ Testing search for "roberto"...');
    const { data: searchResults, error: searchError } = await supabase
      .from('profiles')
      .select('id, username, full_name, age, gender')
      .or('full_name.ilike.%roberto%,username.ilike.%roberto%');
    
    if (searchError) {
      console.log('❌ Search error:', searchError);
    } else {
      console.log(`✅ Found ${searchResults?.length || 0} Roberto users:`);
      searchResults?.forEach(user => {
        console.log(`   • ${user.full_name} (@${user.username}) - ID: ${user.id.substring(0, 8)}...`);
      });
    }

    // 3. Test browse excluding current user (Roberto 1)
    const currentUserId = '1818c8d3-c9a1-4a53-9b01-fc51e45ce55d';
    console.log('\n3️⃣ Testing browse profiles (excluding current user)...');
    const { data: browseResults, error: browseError } = await supabase
      .from('profiles')
      .select('id, username, full_name, age, gender')
      .neq('id', currentUserId)
      .order('created_at', { ascending: false });
    
    if (browseError) {
      console.log('❌ Browse error:', browseError);
    } else {
      console.log(`✅ Found ${browseResults?.length || 0} other users:`);
      browseResults?.forEach(user => {
        console.log(`   • ${user.full_name} (@${user.username}) - ${user.age}y, ${user.gender}`);
      });
    }

    // 4. Check if the other Roberto would be found
    const otherRobertoId = 'c6f6f365-6150-4bf2-96ee-39be2ec41094';
    const otherRoberto = browseResults?.find(user => user.id === otherRobertoId);
    
    console.log('\n4️⃣ Can Roberto 1 discover Roberto 2?');
    if (otherRoberto) {
      console.log('✅ YES! Other Roberto Coscia found in discovery results');
      console.log(`   • ${otherRoberto.full_name} (@${otherRoberto.username})`);
    } else {
      console.log('❌ NO! Other Roberto Coscia NOT found in discovery results');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testDiscovery();
