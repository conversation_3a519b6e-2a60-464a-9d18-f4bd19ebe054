# Scripts Directory

This directory contains all utility scripts, database management files, and testing utilities for the SoulTwinSync project.

## 📁 Directory Structure

### `/database/` - Database Scripts
SQL scripts for database setup, migration, and maintenance:

- **`FINAL_WORKING_FIX.sql`** - ⭐ **MAIN DATABASE SETUP SCRIPT** 
  - Complete database setup and fix script
  - Creates missing tables and columns
  - Fixes RLS policies for 403 Forbidden errors
  - **Run this first in Supabase SQL Editor**

- **Other SQL files** - Additional database fixes and migrations
  - Various specialized fix scripts
  - Schema creation scripts
  - Test data scripts

### `/tests/` - Test Scripts
JavaScript test files for verifying functionality:

- Database connection tests
- User discovery tests  
- Tagging system tests
- End-to-end functionality verification

### `/utilities/` - Utility Scripts
JavaScript/Node.js utilities for maintenance and diagnostics:

- **`.mjs` files** - ES module utilities for database management
- **`.js` files** - Diagnostic and verification scripts
- Database state checking tools
- Data management utilities

## 🚀 Quick Start

1. **First Time Setup**:
   ```bash
   # Run this in Supabase SQL Editor
   scripts/database/FINAL_WORKING_FIX.sql
   ```

2. **Test Everything Works**:
   ```bash
   node scripts/tests/test_database_fixes.mjs
   ```

3. **Check Database State**:
   ```bash
   node scripts/utilities/deep_db_check.mjs
   ```

## ⚠️ Important Notes

- Always backup your database before running SQL scripts
- Run `FINAL_WORKING_FIX.sql` first for complete setup
- Test scripts require valid environment variables
- Utility scripts may need proper Supabase credentials

## 📝 Script Usage

Most scripts can be run with Node.js:
```bash
node scripts/utilities/script-name.mjs
node scripts/tests/test-name.js
```

SQL scripts should be run in Supabase SQL Editor for proper execution context.
