import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVnZ2NjYmtoeHBlbXd1dHNxc3BoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc4MDQ5MDgsImV4cCI6MjA1MzM4MDkwOH0.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testCurrentState() {
    console.log('🔍 Testing current RLS state...\n');

    try {
        // Test 1: Check if we can read from user_interest_tags
        console.log('1️⃣ Testing read access to user_interest_tags...');
        const { data: tags, error: readError } = await supabase
            .from('user_interest_tags')
            .select('*')
            .limit(5);
            
        if (readError) {
            console.error('❌ Read error:', readError.message);
        } else {
            console.log(`✅ Read successful - found ${tags?.length || 0} tags`);
        }

        // Test 2: Check authentication status
        console.log('\n2️⃣ Checking authentication...');
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        
        if (authError || !user) {
            console.log('ℹ️ No authenticated user - this is likely the issue');
            console.log('👉 You need to be logged in to create tags');
            return;
        } else {
            console.log(`✅ Authenticated as: ${user.email}`);
        }

        // Test 3: Try to create a tag
        console.log('\n3️⃣ Testing tag creation...');
        const { data: newTag, error: createError } = await supabase
            .from('user_interest_tags')
            .insert({
                tagger_id: user.id,
                tagged_user_id: user.id,
                tag_type: 'interested',
                emoji: '👍'
            })
            .select();
            
        if (createError) {
            console.error('❌ Create error:', createError.message);
            
            // Analyze the error
            if (createError.message.includes('row-level security')) {
                console.log('🔍 RLS policy is blocking the insert');
                console.log('💡 Solution: Update RLS policies to be more permissive');
            } else if (createError.message.includes('foreign key')) {
                console.log('🔍 Foreign key constraint issue');
                console.log('💡 Solution: Ensure referenced profiles exist');
            }
        } else {
            console.log('✅ Tag creation successful:', newTag);
            
            // Clean up
            const { error: deleteError } = await supabase
                .from('user_interest_tags')
                .delete()
                .eq('id', newTag[0].id);
                
            if (!deleteError) {
                console.log('🧹 Test tag cleaned up');
            }
        }

        // Test 4: Check profiles table
        console.log('\n4️⃣ Testing profiles access...');
        const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('id, username, full_name')
            .eq('id', user.id)
            .single();
            
        if (profileError) {
            console.error('❌ Profile error:', profileError.message);
        } else {
            console.log('✅ Profile access successful:', profile);
        }

    } catch (error) {
        console.error('❌ Unexpected error:', error);
    }
}

testCurrentState();
