import { supabase } from './supabase';
import type { Profile, Interest, PersonalityTrait, RelationshipPreference } from '../types';
import { ErrorLogger } from './errorHandler';

export async function saveProfile(profile: Profile): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('profiles')
      .upsert({
        id: profile.id,
        username: profile.username || profile.name.toLowerCase().replace(/\s+/g, '_'), // Ensure username is handled
        full_name: profile.name,
        avatar_url: profile.avatar_url, // Ensure avatar_url is saved
        location: profile.location,
        photos: (profile.photos || []) as any,
        age: profile.age,
        gender: profile.gender,

        bio: profile.bio,
        // Assuming these are stored as JSON or text arrays in DB. Adjust if schema differs.
        interests: profile.interests as any, 
        personalityTraits: profile.personalityTraits as any,
        relationshipPreferences: profile.relationshipPreferences as any,
        communicationPreferences: profile.communicationPreferences as any, // Added
        boundaries: profile.boundaries as any,
        // values: profile.values as any, // If 'values' is a field
        // preferredConversationTopics: profile.preferredConversationTopics as any, // If exists
        updated_at: new Date().toISOString(),
        twin_context: profile.twin_context,
        allow_persona_learning_from_chats: profile.allow_persona_learning_from_chats,
        // created_at is usually handled by DB default
      });

    if (error) {
      ErrorLogger.logError(new Error(`Supabase error: ${error.message}`), 'database');
      throw error;
    }
    return { success: true };
  } catch (error) {
    ErrorLogger.logError(error, 'database');
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to save profile'
    };
  }
}

export async function getProfile(userId: string): Promise<Profile | null> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) throw error;
    if (!data) return null;

    return {
      id: data.id,
      username: data.username,
      name: data.full_name || data.username, // Fallback for name
      avatar_url: data.avatar_url,
      location: data.location ?? undefined,
      photos: (data.photos as string[] | null) ?? [],
      age: data.age ?? undefined, // Use ?? to convert null to undefined, matching Profile type
      gender: data.gender ?? undefined,

      bio: data.bio ?? undefined,
      interests: (data.interests as Interest[] | string[] | undefined)?.map((i: any) => typeof i === 'string' ? { id: i, name: i, category: '' } : i) ?? [],
      personalityTraits: (data.personalityTraits as PersonalityTrait[] | string[] | undefined)?.map((t: any) => typeof t === 'string' ? { id: t, name: t, value: 0 } : t) ?? [],
      relationshipPreferences: (data.relationshipPreferences as RelationshipPreference[] | string[] | undefined)?.map((r: any) => typeof r === 'string' ? { id: r, type: r, importance: 0 } : r) ?? [],
      communicationPreferences: (data.communicationPreferences as string[] | undefined) ?? [],
      boundaries: (data.boundaries as string[] | undefined) ?? [],
      // values: (data.values as string[] | undefined) ?? [],
      // preferredConversationTopics: (data.preferredConversationTopics as string[] | undefined) ?? [],
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      twin_context: data.twin_context || null,
      allow_persona_learning_from_chats: data.allow_persona_learning_from_chats === undefined ? true : data.allow_persona_learning_from_chats,
    } as Profile; // Cast to Profile to satisfy stricter client type
  } catch (error) {
    ErrorLogger.logError(error as Error, 'database');
    return null;
  }
}

export async function searchUsers(searchTerm: string, currentUserId?: string, location?: string): Promise<{ users: Profile[]; total: number }> {
  try {
    // Get total users count first
    const { count: totalCount } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    // Then search for users matching the term, excluding the current user
    let query = supabase
      .from('profiles')
      .select('*')
      .or(`username.ilike.%${searchTerm}%,full_name.ilike.%${searchTerm}%`)
      .limit(10);
    if (location) {
      query = query.eq('location', location);
    }
    if (currentUserId) {
      query = query.neq('id', currentUserId);
    }
    const { data, error } = await query;

    if (error) throw error;

    const users = (data || []).map(dbProfile => ({
      id: dbProfile.id,
      username: dbProfile.username,
      name: dbProfile.full_name || dbProfile.username,
      avatar_url: dbProfile.avatar_url,
      location: dbProfile.location ?? undefined,
      photos: (dbProfile.photos as string[] | null) ?? [],
      age: dbProfile.age ?? undefined,
      gender: dbProfile.gender ?? undefined,

      bio: dbProfile.bio ?? undefined,
      interests: (dbProfile.interests as Interest[] | string[] | undefined)?.map((i: any) => typeof i === 'string' ? { id: i, name: i, category: '' } : i) ?? [],
      personalityTraits: (dbProfile.personalityTraits as PersonalityTrait[] | string[] | undefined)?.map((t: any) => typeof t === 'string' ? { id: t, name: t, value: 0 } : t) ?? [],
      relationshipPreferences: (dbProfile.relationshipPreferences as RelationshipPreference[] | string[] | undefined)?.map((r: any) => typeof r === 'string' ? { id: r, type: r, importance: 0 } : r) ?? [],
      communicationPreferences: (dbProfile.communicationPreferences as string[] | undefined) ?? [],
      boundaries: (dbProfile.boundaries as string[] | undefined) ?? [],
      createdAt: new Date(dbProfile.created_at),
      updatedAt: new Date(dbProfile.updated_at),
      twin_context: dbProfile.twin_context || null,
      allow_persona_learning_from_chats: dbProfile.allow_persona_learning_from_chats === undefined ? true : dbProfile.allow_persona_learning_from_chats,
    } as Profile)); // Cast to Profile

    return {
      users,
      total: totalCount || 0
    };
  } catch (error) {
    ErrorLogger.logError(error as Error, 'database');
    return { users: [], total: 0 };
  }
}

/**
 * Fetches a paginated list of user profiles based on specified filters,
 * excluding the current user.
 * 
 * @param filters - An object containing filter criteria.
 * @param filters.ageMin - Minimum age for filtering.
 * @param filters.ageMax - Maximum age for filtering.
 * @param filters.gender - Specific gender to filter by.
 * @param pagination - An object containing pagination parameters.
 * @param pagination.page - The current page number (1-indexed).
 * @param pagination.pageSize - The number of profiles to fetch per page.
 * @param currentUserId - The ID of the current user, to exclude them from results.
 * @returns A Promise that resolves to an object containing the list of profiles
 *          and the total count of matching profiles, or null if an error occurs.
 */
export async function browseProfiles(
  filters: { ageMin?: number; ageMax?: number; gender?: string; location?: string },
  pagination: { page: number; pageSize: number },
  currentUserId: string
): Promise<{ profiles: Profile[]; totalCount: number } | null> {
  try {
    let query = supabase
      .from('profiles')
      .select('*', { count: 'exact' }) // Select all profile fields and request total count
      .neq('id', currentUserId); // Exclude the current user from the results

    // Apply age filters if provided
    if (filters.ageMin) {
      query = query.gte('age', filters.ageMin); // Age greater than or equal to ageMin
    }
    if (filters.ageMax) {
      query = query.lte('age', filters.ageMax); // Age less than or equal to ageMax
    }

    // Apply gender filter if provided and is a specific value (not "any" or "all")
    if (filters.gender && filters.gender.toLowerCase() !== 'all' && filters.gender.toLowerCase() !== 'any') {
      query = query.eq('gender', filters.gender);
    }

    if (filters.location) {
      query = query.eq('location', filters.location);
    }

    // Calculate pagination offset
    const offset = (pagination.page - 1) * pagination.pageSize;
    // Apply pagination using range
    query = query.range(offset, offset + pagination.pageSize - 1);

    const { data, error, count } = await query; // Execute the query

    if (error) {
      ErrorLogger.logError(new Error(`Supabase error in browseProfiles: ${error.message}`), 'database');
      throw error; // Rethrow to be caught by the outer catch block
    }

    const mappedProfiles = (data || []).map(dbProfile => ({
      id: dbProfile.id,
      username: dbProfile.username,
      name: dbProfile.full_name || dbProfile.username,
      avatar_url: dbProfile.avatar_url,
      location: dbProfile.location ?? undefined,
      photos: (dbProfile.photos as string[] | null) ?? [],
      age: dbProfile.age ?? undefined,
      gender: dbProfile.gender ?? undefined,

      bio: dbProfile.bio ?? undefined,
      interests: (dbProfile.interests as Interest[] | string[] | undefined)?.map((i: any) => typeof i === 'string' ? { id: i, name: i, category: '' } : i) ?? [],
      personalityTraits: (dbProfile.personalityTraits as PersonalityTrait[] | string[] | undefined)?.map((t: any) => typeof t === 'string' ? { id: t, name: t, value: 0 } : t) ?? [],
      relationshipPreferences: (dbProfile.relationshipPreferences as RelationshipPreference[] | string[] | undefined)?.map((r: any) => typeof r === 'string' ? { id: r, type: r, importance: 0 } : r) ?? [],
      communicationPreferences: (dbProfile.communicationPreferences as string[] | undefined) ?? [],
      boundaries: (dbProfile.boundaries as string[] | undefined) ?? [],
      createdAt: new Date(dbProfile.created_at),
      updatedAt: new Date(dbProfile.updated_at),
      twin_context: dbProfile.twin_context || null,
      allow_persona_learning_from_chats: dbProfile.allow_persona_learning_from_chats === undefined ? true : dbProfile.allow_persona_learning_from_chats,
    } as Profile));

    return { profiles: mappedProfiles, totalCount: count || 0 };

  } catch (error) {
    // Error already logged if it's a Supabase error from the query
    // If it's another type of error, log it here.
    if (!(error instanceof Error && error.message.includes('Supabase error'))) {
         ErrorLogger.logError(error as Error, 'database');
    }
    return null;
  }
}