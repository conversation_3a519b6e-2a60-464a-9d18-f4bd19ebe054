-- Add test users to auth.users
INSERT INTO auth.users (
  id, 
  aud, 
  role, 
  email, 
  encrypted_password, 
  email_confirmed_at, 
  created_at, 
  updated_at, 
  raw_app_meta_data, 
  raw_user_meta_data, 
  is_super_admin, 
  confirmation_token, 
  recovery_token, 
  email_change_token_new, 
  email_change
) VALUES 
  ('550e8400-e29b-41d4-a716-446655440001', 'authenticated', 'authenticated', '<EMAIL>', 'encoded_password', NOW(), NOW(), NOW(), '{"provider": "google", "providers": ["google"]}', '{"full_name": "<PERSON>", "email": "<EMAIL>", "avatar_url": "https://images.unsplash.com/photo-1494790108755-2616b612b830"}', false, '', '', '', ''),
  ('550e8400-e29b-41d4-a716-446655440002', 'authenticated', 'authenticated', '<EMAIL>', 'encoded_password', NOW(), NOW(), NOW(), '{"provider": "google", "providers": ["google"]}', '{"full_name": "<PERSON>", "email": "<EMAIL>", "avatar_url": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e"}', false, '', '', '', ''),
  ('550e8400-e29b-41d4-a716-446655440003', 'authenticated', 'authenticated', '<EMAIL>', 'encoded_password', NOW(), NOW(), NOW(), '{"provider": "google", "providers": ["google"]}', '{"full_name": "Carol Davis", "email": "<EMAIL>", "avatar_url": "https://images.unsplash.com/photo-1438761681033-6461ffad8d80"}', false, '', '', '', '');

-- Add corresponding profiles
INSERT INTO profiles (
  id, 
  username, 
  full_name, 
  avatar_url, 
  age, 
  gender, 
  bio, 
  location,
  interests,
  communication_preferences
) VALUES 
  ('550e8400-e29b-41d4-a716-446655440001', 'alice_johnson', 'Alice Johnson', 'https://images.unsplash.com/photo-1494790108755-2616b612b830', 28, 'female', 'Love hiking, reading, and good coffee. Looking for meaningful connections.', 'San Francisco, CA', '["hiking", "reading", "coffee", "nature"]', '["text", "video_call"]'),
  ('550e8400-e29b-41d4-a716-446655440002', 'bob_smith', 'Bob Smith', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e', 32, 'male', 'Software engineer by day, chef by night. Enjoy traveling and trying new cuisines.', 'New York, NY', '["cooking", "travel", "technology", "food"]', '["text", "voice_call"]'),
  ('550e8400-e29b-41d4-a716-446655440003', 'carol_davis', 'Carol Davis', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80', 26, 'female', 'Artist and yoga instructor. Passionate about creativity and mindfulness.', 'Los Angeles, CA', '["art", "yoga", "meditation", "creativity"]', '["text", "video_call"]');
