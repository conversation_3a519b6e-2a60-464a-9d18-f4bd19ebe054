-- Complete fix for SoulSynk user discovery issues
-- This script creates missing tables and adds test users

-- 1. CREATE MISSING TABLES
CREATE TABLE IF NOT EXISTS public.user_interest_tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tagger_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  tagged_user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  tag_type text NOT NULL CHECK (tag_type IN ('interested', 'very_interested', 'potential_match')),
  emoji text,
  created_at timestamptz DEFAULT now(),
  UNIQUE(tagger_id, tagged_user_id)
);

CREATE TABLE IF NOT EXISTS public.contact_willingness_status (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  is_open_to_contact boolean DEFAULT false,
  status_message text,
  updated_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.user_notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  type text NOT NULL CHECK (type IN ('interest_tag', 'contact_willingness', 'mutual_interest')),
  title text NOT NULL,
  message text NOT NULL,
  data jsonb,
  read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.user_interest_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_willingness_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;

-- Add RLS policies
CREATE POLICY "Users can view interest tags they created or received" ON public.user_interest_tags
  FOR SELECT USING (auth.uid() = tagger_id OR auth.uid() = tagged_user_id);

CREATE POLICY "Users can create interest tags" ON public.user_interest_tags
  FOR INSERT WITH CHECK (auth.uid() = tagger_id);

CREATE POLICY "Users can view all contact willingness status" ON public.contact_willingness_status
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own contact willingness" ON public.contact_willingness_status
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own notifications" ON public.user_notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.user_notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- 2. ADD TEST USERS FOR DISCOVERY TESTING
-- Note: In production, you wouldn't insert directly into auth.users
-- These are for testing purposes only

-- Add test profiles (profiles table only, not auth.users for testing)
INSERT INTO public.profiles (
  id, 
  username, 
  full_name, 
  avatar_url, 
  age, 
  gender, 
  bio, 
  location,
  interests,
  communication_preferences,
  created_at,
  updated_at
) VALUES 
  -- Test Roberto variations for discovery testing
  ('550e8400-e29b-41d4-a716-************', 'roberto_test1', 'Roberto Martinez', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e', 29, 'male', 'Software developer from Spain. Love technology and meeting new people.', 'Madrid, Spain', '["technology", "programming", "travel", "food"]', '["text", "video_call"]', NOW(), NOW()),
  
  ('550e8400-e29b-41d4-a716-************', 'roberto_test2', 'Roberto Silva', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d', 27, 'male', 'Designer and entrepreneur from Brazil. Passionate about creativity and innovation.', 'São Paulo, Brazil', '["design", "business", "art", "music"]', '["text", "voice_call"]', NOW(), NOW()),
  
  -- Other test users with different names
  ('550e8400-e29b-41d4-a716-446655440003', 'alice_johnson', 'Alice Johnson', 'https://images.unsplash.com/photo-1494790108755-2616b612b830', 28, 'female', 'Love hiking, reading, and good coffee. Looking for meaningful connections.', 'San Francisco, CA', '["hiking", "reading", "coffee", "nature"]', '["text", "video_call"]', NOW(), NOW()),
  
  ('550e8400-e29b-41d4-a716-446655440004', 'bob_smith', 'Bob Smith', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e', 32, 'male', 'Software engineer by day, chef by night. Enjoy traveling and trying new cuisines.', 'New York, NY', '["cooking", "travel", "technology", "food"]', '["text", "voice_call"]', NOW(), NOW()),
  
  ('550e8400-e29b-41d4-a716-446655440005', 'carol_davis', 'Carol Davis', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80', 26, 'female', 'Artist and yoga instructor. Passionate about creativity and mindfulness.', 'Los Angeles, CA', '["art", "yoga", "meditation", "creativity"]', '["text", "video_call"]', NOW(), NOW())

ON CONFLICT (id) DO NOTHING; -- Don't insert if ID already exists

-- 3. VERIFY SETUP
SELECT 'TABLES CREATED' as status;
SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%user%' ORDER BY tablename;

SELECT 'TEST PROFILES ADDED' as status;
SELECT id, username, full_name, age, gender, location FROM public.profiles ORDER BY created_at DESC LIMIT 10;
