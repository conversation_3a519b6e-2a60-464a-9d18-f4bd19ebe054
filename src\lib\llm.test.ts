// @ts-nocheck
// This disables TypeScript errors for missing Jest globals in this file.
// src/lib/openrouter.test.ts
import { generateTwinResponse } from './llm';

describe('generateTwinResponse', () => {
  const baseTwinData = {
    name: 'TestTwin',
    personality: ['friendly', 'curious'],
    interests: ['coding', 'music'],
    communicationStyle: ['direct'],
  };

  test('should include twin_context in system prompt if provided', async () => {
    const twinDataWithContext = {
      ...baseTwinData,
      twin_context: { learned_preferences: ['coffee'] },
    };
    await generateTwinResponse('twin1', 'Hello', [], twinDataWithContext);
    const messages = (await generateTwinResponse('twin1', 'Hello', [], twinDataWithContext)).messages;
    const systemPrompt = messages.find((m: any) => m.role === 'system').content;
    expect(systemPrompt).toContain('TestTwin');
    expect(systemPrompt).toContain('"learned_preferences":["coffee"]');
    expect(systemPrompt).toContain('Additional context about your persona evolution');
  });

  test('should NOT include twin_context in system prompt if not provided', async () => {
    await generateTwinResponse('twin1', 'Hello', [], baseTwinData);
    const messages = (await generateTwinResponse('twin1', 'Hello', [], baseTwinData)).messages;
    const systemPrompt = messages.find((m: any) => m.role === 'system').content;
    expect(systemPrompt).toContain('TestTwin');
    expect(systemPrompt).not.toContain('Additional context about your persona evolution');
  });
  
  test('should NOT include twin_context in system prompt if twin_context is an empty object', async () => {
    const twinDataWithEmptyContext = {
      ...baseTwinData,
      twin_context: {},
    };
    await generateTwinResponse('twin1', 'Hello', [], twinDataWithEmptyContext);
    const messages = (await generateTwinResponse('twin1', 'Hello', [], twinDataWithEmptyContext)).messages;
    const systemPrompt = messages.find((m: any) => m.role === 'system').content;
    expect(systemPrompt).not.toContain('Additional context about your persona evolution');
  });
});
