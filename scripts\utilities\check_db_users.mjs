// Quick database check to see what users exist
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVnZ2NjYmtoeHBlbXd1dHNxc3BoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc4MDQ5MDgsImV4cCI6MjA1MzM4MDkwOH0.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabase() {
  console.log('🔍 Checking database for existing users...');
  
  try {    // Check all profiles
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('id, username, full_name, created_at, age, gender')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    
    console.log('📊 Total profiles found:', profiles?.length || 0);
    
    if (profiles && profiles.length > 0) {
      console.log('👥 All profiles in database:');
      profiles.forEach((profile, index) => {        console.log(`#${index + 1}:`, {
          id: profile.id,
          username: profile.username,
          name: profile.full_name,
          created: new Date(profile.created_at).toLocaleString(),
          age: profile.age,
          gender: profile.gender
        });
      });
    } else {
      console.log('❌ No profiles found in database');
    }
    
    // Check what tables exist
    console.log('\n🗃️ Checking if required tables exist...');
    const tables = ['user_interest_tags', 'contact_willingness_status', 'user_notifications'];
    
    for (const tableName of tables) {
      const { data, error } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ Table '${tableName}' missing or inaccessible:`, error.message);
      } else {
        console.log(`✅ Table '${tableName}' exists (${data || 0} records)`);
      }
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error);
  }
}

checkDatabase();
