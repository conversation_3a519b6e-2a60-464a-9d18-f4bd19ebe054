import { DigitalTwin, PersonalityTrait, Interest } from '../types';
import { demoChatTwin } from '../lib/demoData';

// Sample digital twins including our demo twin
const sampleTwins: DigitalTwin[] = [
  demoChatTwin,
  {
    id: 'twin2',
    name: '<PERSON>',
    avatarUrl: '',
    age: 32,
    gender: 'Male',
    bio: 'Tech enthusiast and foodie. When not coding, I\'m exploring new cuisines or planning my next adventure.',
    personalityTraits: [
      { id: 'trait3', name: 'Analytical', description: 'Analyzes situations logically', strength: 6 },
      { id: 'trait4', name: 'Adventurous', description: 'Willing to try new experiences', strength: 8 },
      { id: 'trait7', name: 'Ambitious', description: 'Has strong goals', strength: 8 }
    ],
    interests: [
      { id: 'int2', name: 'Cooking', category: 'Food', level: 'passion' },
      { id: 'int4', name: 'Travel', category: 'Adventure', level: 'passion' },
      { id: 'int7', name: 'Technology', category: 'Professional', level: 'hobby' }
    ],
    communicationStyle: ['Direct', 'Humorous', 'Curious'],
    relationshipGoals: ['Meaningful connection', 'Shared interests', 'Adventure partner'],
    engagementScore: 0,
    compatibilityScore: 85
  }
];

export const getTwins = (): DigitalTwin[] => {
  return sampleTwins;
};

export const getTwinById = (id: string): DigitalTwin | undefined => {
  return sampleTwins.find(twin => twin.id === id);
};