import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useProfile } from '../../context/ProfileContext';
import { useAuth } from './AuthProvider';

interface PersonalityGuardProps {
  children: React.ReactNode;
}

export const PersonalityGuard: React.FC<PersonalityGuardProps> = ({ children }) => {
  const { profile, isLoading: profileLoading } = useProfile();
  const { loading: authLoading } = useAuth();
  const location = useLocation();

  // Routes that don't require personality test completion
  const exemptRoutes = [
    '/personality-test',
    '/profile/create',
    '/settings',
    '/privacy',
    '/login',
    '/auth/callback'
  ];

  const isExemptRoute = exemptRoutes.some(route => 
    location.pathname.startsWith(route)
  );

  const isLoading = authLoading || profileLoading;

  // Check localStorage first for quick validation
  const localStorageComplete = localStorage.getItem('personalityAssessmentComplete') === 'true';

  // Check database profile for comprehensive validation
  const hasCompletedPersonalityTest = Boolean(
    profile?.personality_test_completed_at &&
    profile?.extraversion_score !== null &&
    profile?.agreeableness_score !== null &&
    profile?.conscientiousness_score !== null &&
    profile?.neuroticism_score !== null &&
    profile?.openness_score !== null
  );

  // If localStorage says complete but profile doesn't show it, clear localStorage
  useEffect(() => {
    if (localStorageComplete && profile && !hasCompletedPersonalityTest) {
      console.log('Clearing localStorage - personality test not found in profile');
      localStorage.removeItem('personalityAssessmentComplete');
    }
  }, [localStorageComplete, profile, hasCompletedPersonalityTest]);

  // Check if user has a basic profile (allows access without personality test)
  const hasBasicProfile = Boolean(
    profile &&
    profile.name &&
    profile.interests &&
    profile.interests.length > 0
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  // If user is on an exempt route, don't check personality test
  if (isExemptRoute) {
    return <>{children}</>;
  }

  // Determine if personality test is complete (use localStorage as fallback during loading)
  const isPersonalityTestComplete = hasCompletedPersonalityTest || (profileLoading && localStorageComplete);

  // If user has completed the personality test, always allow access (no redirection)
  if (isPersonalityTestComplete) {
    return <>{children}</>;
  }

  // Only redirect to personality test if user has NO profile at all AND hasn't completed personality test
  // AND we're not still loading the profile data AND we're not already on the personality test page
  // Users with basic profiles can access all features without personality test
  if (!profileLoading && !hasBasicProfile && !isPersonalityTestComplete && location.pathname !== '/personality-test') {
    return (
      <Navigate 
        to="/personality-test" 
        state={{ 
          from: location,
          message: "Complete your personality assessment to unlock your digital twin's full potential and start discovering compatible connections!"
        }} 
        replace 
      />
    );
  }

  // If user has a basic profile, allow access without personality test requirement
  // This ensures users can navigate freely once they have any kind of profile

  return <>{children}</>;
};
