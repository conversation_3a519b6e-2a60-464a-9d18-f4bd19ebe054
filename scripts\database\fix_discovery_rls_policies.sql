-- Fix User Discovery RLS Policies
-- The issue is that current SELECT policies only allow users to see their own profiles
-- We need to add a policy that allows users to discover other profiles

-- First, let's see current policies on profiles table
SELECT 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'profiles' AND cmd = 'SELECT';

-- Drop the restrictive SELECT policies that only allow viewing own profile
DROP POLICY IF EXISTS "Profiles: Select own row" ON public.profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;

-- Create new comprehensive SELECT policies
-- Policy 1: Users can view their own profile (for profile management)
CREATE POLICY "Users can view own profile"
  ON public.profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Policy 2: Users can discover other profiles (for user discovery)
CREATE POLICY "Users can discover profiles"
  ON public.profiles FOR SELECT
  TO authenticated
  USING (true); -- Allow viewing all profiles for discovery

-- Alternative more restrictive policy if you want to add conditions:
-- CREATE POLICY "Users can discover profiles"
--   ON public.profiles FOR SELECT
--   TO authenticated
--   USING (
--     auth.uid() = id OR  -- Can see own profile
--     (age IS NOT NULL AND full_name IS NOT NULL) -- Can see complete profiles
--   );

-- Also allow anonymous/public users to see profiles (for public discovery)
CREATE POLICY "Public can view profiles"
  ON public.profiles FOR SELECT
  TO public
  USING (true);

-- Verify the new policies
SELECT 'NEW POLICIES CREATED:' as status;
SELECT 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual
FROM pg_policies 
WHERE tablename = 'profiles' AND cmd = 'SELECT'
ORDER BY policyname;

-- Test the fix by checking if we can see all profiles
SELECT 'PROFILE DISCOVERY TEST:' as status;
SELECT 
  id,
  username,
  full_name,
  age,
  gender,
  LEFT(id::text, 8) || '...' as short_id
FROM public.profiles
ORDER BY created_at DESC;
