import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { RealtimeChannel } from '@supabase/supabase-js';
import { Conversation, DigitalTwin, Message, Profile, Milestone } from '../types';
import { supabase } from '../../src/lib/supabase';
import { ErrorLogger } from '../../src/lib/errorHandler';
import { generateTwinResponse } from '../lib/llm';
import { useToast } from '../hooks/useToast';
import { useAuth } from '../hooks/useAuth.ts';
import {
  fetchUserConversations,
  saveConversation as saveConversationToDb,
  saveMessage as saveMessageToDb,
} from '../../src/lib/conversationStorage.ts';
import { saveUserFact, getUserFacts, saveUserSummary, getUserSummary, rescanAndExtractUserFacts } from '../lib/dataManagement';

interface ConversationContextType {
  conversations: Conversation[];
  activeConversation: Conversation | null;
  isLoadingConversations: boolean;
  isLoadingMessages: boolean;
  milestones: Record<string, Milestone[]>;
  startConversation: (participant: DigitalTwin | Profile, type: 'twin' | 'real_user') => Promise<Conversation | null>;
  sendMessage: (conversationId: string, content: string) => Promise<void>;
  getConversation: (conversationId: string) => Conversation | undefined;
  setActiveConversation: (conversationId: string | null) => void;
  trackMilestone: (conversationId: string, milestone: Milestone) => void;
  getMilestones: (conversationId: string) => Milestone[];
  getEngagementScore: (conversationId: string) => number;
  getSuggestedNextSteps: (conversationId: string) => string[];
  deleteConversation: (conversationId: string) => Promise<void>;
  deleteAllConversations: () => Promise<void>;
}

const ConversationContext = createContext<ConversationContextType | undefined>(undefined);

// Store participant info in memory since it's not part of Conversation type
const conversationParticipants = new Map<string, {
  participantId: string;
  participantName: string;
  participantType: 'twin' | 'real_user';
  twinId?: string;
}>();

export const ConversationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversationState] = useState<Conversation | null>(null);
  const [isLoadingConversations, setIsLoadingConversations] = useState<boolean>(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState<boolean>(false);
  const [milestones, setMilestones] = useState<Record<string, Milestone[]>>({});
  const [activeMessageChannel, setActiveMessageChannel] = useState<RealtimeChannel | null>(null);
  const { showError } = useToast();
  const { user } = useAuth();
  const loadingConversationsRef = useRef(false);
  const hasLoadedConversations = useRef(false);

  /**
   * Helper function to add or update a message in the state for a specific conversation.
   */
  const addMessageToConversationState = useCallback((newMessage: Message, targetConversationId: string) => {
    const updateMessages = (messages: Message[] | undefined) =>
      [...(messages || []).filter(m => m.id !== newMessage.id), newMessage]
        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    setConversations(prevConvs =>
      prevConvs.map(conv =>
        conv.id === targetConversationId
          ? {
              ...conv,
              messages: updateMessages(conv.messages),
              lastActive: new Date(newMessage.timestamp),
            }
          : conv
      )
    );

    setActiveConversationState(prevActive =>
      prevActive && prevActive.id === targetConversationId
        ? {
            ...prevActive,
            messages: updateMessages(prevActive.messages),
            lastActive: new Date(newMessage.timestamp),
          }
        : prevActive
    );
  }, []);

  /**
   * Load conversations from the database
   */
  const loadConversations = useCallback(async () => {
    if (!user || loadingConversationsRef.current) return;

    loadingConversationsRef.current = true;
    setIsLoadingConversations(true);

    try {
      const userConversations = await fetchUserConversations(user.id);
      setConversations(userConversations);
      hasLoadedConversations.current = true;
    } catch (error) {
      ErrorLogger.logError(error as Error, 'database', 'Failed to load conversations');
      showError('Failed to load conversations');
    } finally {
      setIsLoadingConversations(false);
      loadingConversationsRef.current = false;
    }
  }, [user, showError]);

  /**
   * Load messages for a specific conversation
   */
  const loadMessages = useCallback(async (conversationId: string) => {
    setIsLoadingMessages(true);

    try {
      const messages = await fetchUserConversations(conversationId);

      setConversations(prevConvs =>
        prevConvs.map(conv =>
          conv.id === conversationId
            ? { ...conv, messages: messages as unknown as Message[] }
            : conv
        )
      );

      if (activeConversation?.id === conversationId) {
        setActiveConversationState(prev => prev ? { ...prev, messages: messages as unknown as Message[] } : null);
      }
    } catch (error) {
      ErrorLogger.logError(error as Error, 'database', 'Failed to load messages');
      showError('Failed to load messages');
    } finally {
      setIsLoadingMessages(false);
    }
  }, [activeConversation?.id, showError]);

  /**
   * Start a new conversation
   */
  const startConversation = useCallback(async (
    participant: DigitalTwin | Profile,
    type: 'twin' | 'real_user'
  ): Promise<Conversation | null> => {
    if (!user) return null;

    try {
      // Support both twin and real user conversations
      const newConversation: Conversation = {
        id: crypto.randomUUID(),
        userId: user.id,
        digitalTwin: type === 'twin' ? participant as DigitalTwin : undefined,
        realUser: type === 'real_user' ? participant as Profile : undefined,
        messages: [],
        createdAt: new Date(),
        lastActive: new Date(),
        engagementScore: 0,
        type: type,
      };

      // Store participant info separately
      conversationParticipants.set(newConversation.id, {
        participantId: participant.id,
        participantName: participant.name,
        participantType: type,
        twinId: type === 'twin' ? (participant as DigitalTwin).id : undefined,
      });

      await saveConversationToDb(newConversation, user.id); // Only pass the newConversation object
      setConversations(prev => [...prev, newConversation]);
      setActiveConversationState(newConversation);

      return newConversation;
    } catch (error) {
      ErrorLogger.logError(error as Error, 'database', 'Failed to start conversation');
      showError('Failed to start conversation');
      return null;
    }
  }, [user, showError]);

  /**
   * Send a message in a conversation
   */
  const sendMessage = useCallback(async (conversationId: string, content: string) => {
    if (!user) return;

    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation) return;

    const userMessage: Message = {
      id: crypto.randomUUID(),
      conversationId,
      senderId: user.id,
      sender: 'user',
      content,
      timestamp: new Date(),
      status: 'sent',
      reactions: []
    } as Message;

    try {
      // Add user message to state
      addMessageToConversationState(userMessage, conversationId);

      // Save to database with currentUserId
      await saveMessageToDb(userMessage);
      // Improved fact extraction: extract genres, artists, and preferences from every message
      const musicRegex = /(electronic music|sidechain|techno|house|music|musica elettronica|synth|massive attack|tycho|pop|rock|jazz|blues|classical|indie|ambient|trance|drum and bass|dubstep|trap|hip hop|rap|metal|punk|folk|country|reggae|reggaeton|latin|k-pop|j-pop|edm|dance|orchestra|opera|soundtrack|colonna sonora|artist: [^,.;!?\n]+)/gi;
      const matches = content.match(musicRegex);
      if (matches) {
        for (const match of matches) {
          await saveUserFact(user.id, `music_preference: ${match}`);
        }
      }
      // --- NEW: Summarize all user conversations and update summary ---
      const allConvs = await fetchUserConversations(user.id);
      // If Conversation does not have a 'deleted' property, just use all conversations
      let allMessages = allConvs.flatMap(c => c.messages || []);
      let allText = allMessages.map(m => m.content).join(' ');
      // Simple summary: just join all facts and last 10 messages
      let summary = '';
      if (allText.length > 0) {
        summary = `User has discussed: ${allText.slice(-1000)}.`;
      }
      await saveUserSummary(user.id, summary);
      // --- END NEW ---
      // Fetch all user facts and summary for persistent memory
      const userFacts = await getUserFacts(user.id);
      const userSummary = await getUserSummary(user.id);
      // Inject user facts and summary into the system prompt
      const userFactsPrompt = userFacts.length > 0 ? `\nUser facts: ${userFacts.join('; ')}` : '';
      const userSummaryPrompt = userSummary ? `\nUser summary: ${userSummary}` : '';

      // Check if it's a twin conversation
      const participantInfo = conversationParticipants.get(conversationId);
      const isTwinConversation = participantInfo?.participantType === 'twin' || conversation.digitalTwin;
      const twinId = participantInfo?.twinId || conversation.digitalTwin?.id;

      if (isTwinConversation && twinId && (participantInfo || conversation.digitalTwin)) {
        const twinMessage: Message = {
          id: crypto.randomUUID(),
          conversationId,
          senderId: twinId,
          sender: 'twin',
          content: '', // Will be filled by generateTwinResponse
          timestamp: new Date(),
          status: 'sending',
          reactions: []
        } as Message;

        // Add placeholder message
        addMessageToConversationState(twinMessage, conversationId);

        // Get the message history as strings
        const messageHistory = (conversation.messages || []).map(m => m.content);        // Generate response with minimal twin data and user facts/summary
        const response = await generateTwinResponse(
          twinId,
          content,
          messageHistory,
          {
            name: participantInfo?.participantName || conversation.digitalTwin?.name || 'Twin',
            personality: (conversation.digitalTwin?.personalityTraits || []).map(trait => trait.name),
            interests: (conversation.digitalTwin?.interests || []).map(interest => interest.name),
            communicationStyle: conversation.digitalTwin?.communicationStyle || [],
            twin_context: userFactsPrompt + userSummaryPrompt // inject only relational memory here
          }
        );

        // Update with actual response
        const updatedTwinMessage = { ...twinMessage, content: response, status: 'sent' as const } as Message;
        addMessageToConversationState(updatedTwinMessage, conversationId);

        // Save to database with currentUserId
        await saveMessageToDb(updatedTwinMessage); // Only pass the updatedTwinMessage object
      }
    } catch (error) {
      ErrorLogger.logError(error as Error, 'database', 'Failed to send message');
      showError('Failed to send message');
    }
  }, [user, conversations, addMessageToConversationState, showError]);

  /**
   * Get a conversation by ID
   */
  const getConversation = useCallback((conversationId: string): Conversation | undefined => {
    return conversations.find(c => c.id === conversationId);
  }, [conversations]);

  /**
   * Set the active conversation
   */
  const setActiveConversation = useCallback(async (conversationId: string | null) => {
    // Clean up previous channel
    if (activeMessageChannel) {
      await supabase.removeChannel(activeMessageChannel);
      setActiveMessageChannel(null);
    }

    if (!conversationId) {
      setActiveConversationState(null);
      return;
    }

    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
      setActiveConversationState(conversation);

      // Load messages if not loaded
      if (!conversation.messages || conversation.messages.length === 0) {
        await loadMessages(conversationId);
      }

      // Set up realtime subscription for new messages
      const channel = supabase
        .channel(`conversation:${conversationId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `conversation_id=eq.${conversationId}`,
          },
          (payload) => {
            const newMessage = payload.new as Message;
            addMessageToConversationState(newMessage, conversationId);
          }
        )
        .subscribe();

      setActiveMessageChannel(channel);
    }
  }, [conversations, activeMessageChannel, loadMessages, addMessageToConversationState]);

  /**
   * Track a milestone in a conversation
   */
  const trackMilestone = useCallback((conversationId: string, milestone: Milestone) => {
    setMilestones(prev => ({
      ...prev,
      [conversationId]: [...(prev[conversationId] || []), milestone]
    }));
  }, []);

  /**
   * Get milestones for a conversation
   */
  const getMilestones = useCallback((conversationId: string): Milestone[] => {
    return milestones[conversationId] || [];
  }, [milestones]);

  /**
   * Calculate engagement score for a conversation
   */
  const getEngagementScore = useCallback((conversationId: string): number => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation || !conversation.messages) return 0;

    const messageCount = conversation.messages.length;
    const conversationMilestones = getMilestones(conversationId);
    const milestoneScore = conversationMilestones.length * 10;

    // Simple scoring: 1 point per message + 10 points per milestone
    return Math.min(100, messageCount + milestoneScore);
  }, [conversations, getMilestones]);

  /**
   * Get suggested next steps for a conversation
   */
  const getSuggestedNextSteps = useCallback((conversationId: string): string[] => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation) return [];

    const messageCount = conversation.messages?.length || 0;
    const suggestions: string[] = [];

    if (messageCount === 0) {
      suggestions.push('Start the conversation with a greeting');
    } else if (messageCount < 5) {
      suggestions.push('Ask about their interests or hobbies');
      suggestions.push('Share something about yourself');
    } else if (messageCount < 10) {
      suggestions.push('Dive deeper into a shared interest');
      suggestions.push('Plan a virtual or in-person activity');
    } else {
      suggestions.push('Reflect on your conversation journey');
      suggestions.push('Set a goal for your next interaction');
    }

    return suggestions;
  }, [conversations]);

  /**
   * Delete a conversation
   */
  const deleteConversation = useCallback(async (conversationId: string) => {
    if (!user) return;

    try {
      // Remove or replace deleteConversationFromDb call as it is not exported
      // await deleteConversationFromDb(conversationId);
      setConversations(prev => prev.filter(c => c.id !== conversationId));
      conversationParticipants.delete(conversationId);

      if (activeConversation?.id === conversationId) {
        setActiveConversationState(null);
      }
    } catch (error) {
      ErrorLogger.logError(error as Error, 'database', 'Failed to delete conversation');
      showError('Failed to delete conversation');
    }
  }, [user, activeConversation?.id, showError]);

  /**
   * Delete all conversations
   */
  const deleteAllConversations = useCallback(async () => {
    if (!user) return;

    try {
      // Delete all conversations for the user
      // Remove or replace deleteConversationFromDb call as it is not exported
      // for (const conversation of conversations) {
      //   await deleteConversationFromDb(conversation.id);
      // }

      setConversations([]);
      setActiveConversationState(null);
      setMilestones({});
      conversationParticipants.clear();
    } catch (error) {
      ErrorLogger.logError(error as Error, 'database', 'Failed to delete all conversations');
      showError('Failed to delete all conversations');
    }
  }, [user, conversations, showError]);

  // Load conversations on mount and when user changes - but only once
  useEffect(() => {
    if (user && !hasLoadedConversations.current && !loadingConversationsRef.current) {
      loadConversations();
      // Rescan all messages for facts on login/startup
      rescanAndExtractUserFacts(user.id);
    }
  }, [user, loadConversations]);

  // Cleanup realtime subscription on unmount
  useEffect(() => {
    return () => {
      if (activeMessageChannel) {
        supabase.removeChannel(activeMessageChannel);
      }
    };
  }, [activeMessageChannel]);

  return (
    <ConversationContext.Provider
      value={{
        conversations,
        activeConversation,
        isLoadingConversations,
        isLoadingMessages,
        milestones,
        startConversation,
        sendMessage,
        getConversation,
        setActiveConversation,
        trackMilestone,
        getMilestones,
        getEngagementScore,
        getSuggestedNextSteps,
        deleteConversation,
        deleteAllConversations
      }}
    >
      {children}
    </ConversationContext.Provider>
  );
};

export const useConversation = () => {
  const context = useContext(ConversationContext);
  if (context === undefined) {
    throw new Error('useConversation must be used within a ConversationProvider');
  }
  return context;
};