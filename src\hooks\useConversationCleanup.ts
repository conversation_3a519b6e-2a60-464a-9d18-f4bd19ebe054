import { useEffect } from 'react';
import { useConversation } from '../context/ConversationContext';
import { supabase } from '../lib/supabase';

export const useConversationCleanup = () => {
  const { conversations } = useConversation();

  useEffect(() => {
    const cleanupInactiveConversations = async () => {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      const { data: inactiveConversations, error } = await supabase
        .from('conversations')
        .select('id')
        .lt('updated_at', oneWeekAgo.toISOString());

      if (error) {
        console.error('Error fetching inactive conversations:', error);
        return;
      }

      if (inactiveConversations.length > 0) {
        const { error: deleteError } = await supabase
          .from('conversations')
          .delete()
          .in('id', inactiveConversations.map(conv => conv.id));

        if (deleteError) {
          console.error('Error deleting inactive conversations:', deleteError);
        }
      }
    };

    // Run cleanup every day
    const interval = setInterval(cleanupInactiveConversations, 24 * 60 * 60 * 1000);

    // Run once on mount
    cleanupInactiveConversations();

    return () => clearInterval(interval);
  }, []);
};