import React from 'react';
import { ArrowRight } from 'lucide-react';

interface NextStepSuggestionsProps {
  suggestions: string[];
}

export const NextStepSuggestions: React.FC<NextStepSuggestionsProps> = ({ suggestions }) => {
  if (!suggestions.length) return null;
  
  return (
    <div className="flex justify-center my-6">
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-100 rounded-lg p-4 max-w-md w-full animate-fadeIn">
        <h4 className="text-sm font-semibold text-purple-800 mb-2">Suggested Next Steps</h4>
        <p className="text-xs text-gray-600 mb-3">
          Your conversation is going well! Consider taking these next steps:
        </p>
        <div className="space-y-2">
          {suggestions.map((suggestion, index) => (
            <div 
              key={index}
              className="flex items-center text-sm bg-white rounded-lg p-2 border border-purple-100"
            >
              <ArrowRight className="h-4 w-4 text-purple-500 mr-2 flex-shrink-0" />
              <span className="text-purple-900">{suggestion}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};