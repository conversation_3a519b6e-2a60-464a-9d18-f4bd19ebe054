-- Quick diagnostic script to check current database state
-- Run this in Supabase SQL Editor to see what constraints exist

-- Check current constraints on user_interest_tags table
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(c.oid) as constraint_definition
FROM pg_constraint c
JOIN pg_class t ON c.conrelid = t.oid
WHERE t.relname = 'user_interest_tags'
AND contype = 'c'; -- Check constraints only

-- Check if the table exists and its structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_interest_tags'
ORDER BY ordinal_position;

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'user_interest_tags';
