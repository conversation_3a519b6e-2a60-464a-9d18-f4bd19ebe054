import React, { useState } from 'react'; // Added useState
import { Shield, Lock, Database, Download, Trash2, AlertTriangle } from 'lucide-react'; // Added AlertTriangle
import { useAuth } from '../hooks/useAuth';
import { useToast } from '../hooks/useToast';
import exportUserData from '../../src/pages/PrivacyPolicy'; // Changed to default import
import deleteUserData from '../../src/pages/PrivacyPolicy'; // Added default import for deleteUserData
import { useNavigate } from 'react-router-dom'; // Added for redirection

const PrivacyPolicy: React.FC = () => {
  const { user } = useAuth();
  const { showSuccess, showError } = useToast();
  const navigate = useNavigate(); // Added for redirection
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false); // State for delete confirmation modal

  const handleDataExport = async () => {
    if (!user) {
      showError('You must be logged in to export data.');
      return;
    }
    try {
      const data = await exportUserData(user.id);
      const jsonString = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonString], { type: "application/json" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = "userData.json";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      showSuccess('Your data has been exported and download should start shortly.');
    } catch (error) {
      console.error('Export error:', error);
      showError(error instanceof Error ? error.message : 'Failed to export data. Please try again.');
    }
  };

  const handleDataDeletion = () => { // No longer async, just shows modal
    if (!user) {
      showError('You must be logged in to delete data.');
      return;
    }
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!user) {
      showError('You must be logged in to delete data.');
      return;
    }
    try {
      await deleteUserData(user.id);
      showSuccess('Your account has been permanently deleted.');
      // Optionally, redirect or refresh after deletion
      navigate('/'); // Redirect to homepage or login
    } catch (error) {
      console.error('Deletion error:', error);
      showError(error instanceof Error ? error.message : 'Failed to delete account. Please try again.');
    } finally {
      setShowDeleteConfirm(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-10 h-10 text-red-500 mr-3" />
              <h2 className="text-xl font-bold text-gray-800">Confirm Deletion</h2>
            </div>
            <p className="text-gray-700 mb-6">
              Are you sure you want to permanently delete your account and all associated data? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 rounded-lg text-gray-700 bg-gray-200 hover:bg-gray-300 transition"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-700 transition"
              >
                Confirm Deletion
              </button>
            </div>
          </div>
        </div>
      )}
      <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md overflow-hidden">
        <div className="bg-gradient-to-r from-purple-600 to-pink-500 px-6 py-4">
          <div className="flex items-center">
            <Shield className="w-8 h-8 text-white mr-3" />
            <h1 className="text-2xl font-bold text-white">Privacy Policy & Data Management</h1>
          </div>
        </div>
        
        <div className="p-6">
          <div className="prose max-w-none">
            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <Lock className="w-5 h-5 text-purple-500 mr-2" />
                AI Training & Data Usage
              </h2>
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h3 className="font-medium text-gray-800 mb-2">How We Use Your Data</h3>
                <ul className="list-disc pl-5 text-gray-600 space-y-2">
                  <li>Chat history is used to train your digital twin's personality.</li>
                  <li>The chat remembers the things you say during your session, and all sessions you add are saved in the database for future reference.</li>
                  <li>WhatsApp chat imports are processed to understand communication patterns.</li>
                  <li>Profile information helps create accurate twin responses.</li>
                  <li>An evolving learning profile (persona context, or `twin_context`) is maintained and updated based on your interactions to continuously personalize your digital twin's responses and understanding. This learning profile is considered part of your personal data; you can export it and it will be permanently deleted if you choose to delete your account.</li>
                  <li>All data is encrypted and stored securely.</li>
                </ul>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <Database className="w-5 h-5 text-purple-500 mr-2" />
                Data Storage & Security
              </h2>
              <div className="space-y-4 text-gray-600">
                <p>We implement strong security measures to protect your data:</p>
                <ul className="list-disc pl-5 space-y-2">
                  <li>End-to-end encryption for all chat messages</li>
                  <li>Secure storage of WhatsApp chat history</li>
                  <li>Regular security audits and updates</li>
                  <li>Strict access controls and authentication</li>
                </ul>
              </div>
            </section>

            {user && (
              <section className="mt-8 pt-8 border-t border-gray-200">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Your Data Rights</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="font-medium text-gray-800 mb-2 flex items-center">
                      <Download className="w-5 h-5 text-purple-500 mr-2" />
                      Export Your Data
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Download a copy of all your personal data, including chat history, twin configurations, and your learning profile (persona context).
                    </p>
                    <button
                      onClick={handleDataExport}
                      className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition"
                    >
                      Request Data Export
                    </button>
                  </div>

                  <div className="bg-red-50 rounded-lg p-6">
                    <h3 className="font-medium text-red-800 mb-2 flex items-center">
                      <Trash2 className="w-5 h-5 text-red-500 mr-2" />
                      Delete Your Data
                    </h3>
                    <p className="text-red-600 mb-4">
                      Permanently delete all your data and account information. This action cannot be undone.
                    </p>
                    <button
                      onClick={handleDataDeletion}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition"
                    >
                      Request Account Deletion
                    </button>
                  </div>
                </div>
              </section>
            )}

            <section className="mt-8 pt-8 border-t border-gray-200">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">GDPR Compliance</h2>
              <div className="space-y-4 text-gray-600">
                <p>Under GDPR, you have the following rights:</p>
                <ul className="list-disc pl-5 space-y-2">
                  <li>Right to access your personal data</li>
                  <li>Right to rectify inaccurate data</li>
                  <li>Right to erasure ("right to be forgotten")</li>
                  <li>Right to restrict processing</li>
                  <li>Right to data portability</li>
                  <li>Right to object to processing</li>
                </ul>
                <p className="mt-4">
                  To exercise any of these rights, please contact our Data Protection Officer at{' '}
                  <a href="mailto:<EMAIL>" className="text-purple-600 hover:text-purple-800">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </section>

            <div className="mt-8 pt-6 border-t border-gray-200 text-gray-500 text-sm">
              <p>Last updated: March 15, 2024</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;