import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Search, MessageSquare, Calendar, ArrowUpDown, Trash2 } from 'lucide-react';
import { useConversation } from '../context/ConversationContext';
import { useToast } from '../hooks/useToast';
import { Conversation } from '../types';
import { formatDate } from '../utils/dateUtils';
import DeleteConfirmationModal from '../components/conversation/DeleteConfirmationModal';

type SortOption = 'recent' | 'oldest' | 'mostMessages' | 'leastMessages';

const Conversations: React.FC = () => {
  const { conversations, deleteConversation, deleteAllConversations } = useConversation();
  const { showSuccess, showError } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('recent');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [showDeleteAllModal, setShowDeleteAllModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Filter conversations based on search term
  const filteredConversations = conversations.filter(conversation => {
    // Prefer digitalTwin, fallback to realUser, else skip
    const participant = conversation.digitalTwin || conversation.realUser;
    if (!participant || typeof participant.name !== 'string') return false;
    return participant.name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Sort conversations based on selected option
  const sortedConversations = [...filteredConversations].sort((a, b) => {
    // Defensive: handle missing lastActive or messages
    const aLast = a.lastActive ? new Date(a.lastActive).getTime() : 0;
    const bLast = b.lastActive ? new Date(b.lastActive).getTime() : 0;
    const aMsgLen = Array.isArray(a.messages) ? a.messages.length : 0;
    const bMsgLen = Array.isArray(b.messages) ? b.messages.length : 0;
    switch (sortBy) {
      case 'recent':
        return bLast - aLast;
      case 'oldest':
        return aLast - bLast;
      case 'mostMessages':
        return bMsgLen - aMsgLen;
      case 'leastMessages':
        return aMsgLen - bMsgLen;
      default:
        return 0;
    }
  });

  const handleDeleteAll = async () => {
    setIsDeleting(true);
    try {
      await deleteAllConversations();
      showSuccess('All conversations deleted successfully');
      setShowDeleteAllModal(false);
    } catch (error) {
      showError('Failed to delete conversations');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteConversation = async (conversationId: string) => {
    try {
      await deleteConversation(conversationId);
      showSuccess('Conversation deleted successfully');
    } catch (error) {
      showError('Failed to delete conversation');
    }
  };

  const renderEmptyState = () => (
    <div className="text-center py-12">
      <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
        <MessageSquare className="w-8 h-8 text-purple-500" />
      </div>
      <h3 className="text-lg font-semibold text-gray-800 mb-2">No conversations yet</h3>
      <p className="text-gray-500 mb-6">Start chatting with your matches to see conversations here</p>
      <Link 
        to="/dashboard" 
        className="inline-flex items-center px-5 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition duration-200"
      >
        Find Matches
      </Link>
    </div>
  );

  const renderConversationCard = (conversation: Conversation) => {
    const participant = conversation.digitalTwin || conversation.realUser;
    // Defensive: fallback if participant is missing
    const displayName = participant?.name || 'Unknown';
    const displayInitial = displayName.charAt(0) || '?';
    const participantAvatar =
      conversation.digitalTwin?.avatar || conversation.realUser?.avatar_url;
    // Defensive: handle missing or malformed messages
    const messages = Array.isArray(conversation.messages) ? conversation.messages : [];
    const lastMessage = messages[messages.length - 1];
    // Handle age: could be number, string, or missing
    let ageDisplay = '';
    if (participant && 'age' in participant && participant.age !== undefined && participant.age !== null) {
      ageDisplay = typeof participant.age === 'number' ? String(participant.age) : participant.age;
    }
    // Handle interests: could be array of objects, array of strings, string, or missing
    let interests: any[] = [];
    if (participant && 'interests' in participant && participant.interests) {
      if (Array.isArray(participant.interests)) {
        interests = participant.interests.map((i: any) => {
          if (typeof i === 'string') return { id: i, name: i };
          if (typeof i === 'object' && i !== null) return { id: i.id || i.name || i, name: i.name || i.id || String(i) };
          return { id: String(i), name: String(i) };
        });
      } else if (typeof participant.interests === 'string') {
        interests = (participant.interests as string).split(',').map((s: string) => ({ id: s.trim(), name: s.trim() }));
      }
    }

    return (
      <div 
        key={conversation.id}
        className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition duration-200 overflow-hidden group"
      >
        <div className="p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-4">
              {participantAvatar ? (
                <img
                  src={participantAvatar}
                  alt={displayName}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white font-bold shadow-inner">
                  {displayInitial}
                </div>
              )}
            </div>
            <div className="flex-grow min-w-0">
              <div className="flex justify-between items-center">
                <h3 className="font-semibold text-gray-800">
                  {displayName}{ageDisplay ? `, ${ageDisplay}` : ''}
                </h3>
                <div className="flex items-center text-gray-500 text-xs">
                  <Calendar className="w-3 h-3 mr-1" />
                  <span>{formatDate(conversation.lastActive)}</span>
                </div>
              </div>
              
              <div className="mt-1 flex items-center text-xs text-gray-500">
                <span className="bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">
                  {conversation.messages.length} messages
                </span>
                <span className="mx-2">•</span>
                <span>
                  {conversation.engagementScore > 5 ? 'High engagement' : 'Building connection'}
                </span>
              </div>
              
              {lastMessage ? (
                <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                  <span className="font-medium">
                    {lastMessage.sender === 'user' ? 'You: ' : `${displayName}: `}
                  </span>
                  {lastMessage.content}
                </p>
              ) : (
                <p className="text-sm text-gray-500 mt-2 italic">
                  Start a conversation
                </p>
              )}
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-2 border-t border-gray-100 flex justify-between items-center">
          <div className="flex space-x-1">
            {interests.slice(0, 2).map((interest) => (
              <span 
                key={interest.id} 
                className="px-2 py-0.5 bg-gray-200 text-gray-700 text-xs rounded-full"
              >
                {interest.name}
              </span>
            ))}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={(e) => {
                e.preventDefault();
                handleDeleteConversation(conversation.id);
              }}
              className="text-red-500 hover:text-red-600 p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
              title="Delete conversation"
            >
              <Trash2 className="w-4 h-4" />
            </button>
            <Link 
              to={`/conversations/${conversation.id}`}
              className="text-purple-600 hover:text-purple-700 font-medium text-sm"
            >
              Continue →
            </Link>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Your Conversations</h1>
          <div className="flex items-center space-x-4">
            {conversations.length > 0 && (
              <button
                onClick={() => setShowDeleteAllModal(true)}
                className="text-red-600 hover:text-red-700 font-medium text-sm flex items-center"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                Delete All
              </button>
            )}
            <Link 
              to="/dashboard" 
              className="text-purple-600 hover:text-purple-700 font-medium text-sm"
            >
              Find New Matches →
            </Link>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center">
            <div className="relative flex-grow mb-3 sm:mb-0">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input 
                type="text" 
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500" 
                placeholder="Search conversations..." 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex sm:ml-4">
              <button 
                onClick={() => setIsFilterOpen(!isFilterOpen)}
                className="flex items-center text-gray-700 bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg transition duration-200"
              >
                <ArrowUpDown className="h-4 w-4 mr-2" />
                <span>Sort</span>
              </button>
            </div>
          </div>
          
          {isFilterOpen && (
            <div className="mt-4 pt-4 border-t border-gray-200 grid grid-cols-2 sm:grid-cols-4 gap-2">
              <button 
                onClick={() => setSortBy('recent')}
                className={`px-3 py-2 rounded-lg text-sm font-medium text-center transition-colors ${
                  sortBy === 'recent' 
                    ? 'bg-purple-100 text-purple-700 border border-purple-200' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Most Recent
              </button>
              <button 
                onClick={() => setSortBy('oldest')}
                className={`px-3 py-2 rounded-lg text-sm font-medium text-center transition-colors ${
                  sortBy === 'oldest' 
                    ? 'bg-purple-100 text-purple-700 border border-purple-200' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Oldest First
              </button>
              <button 
                onClick={() => setSortBy('mostMessages')}
                className={`px-3 py-2 rounded-lg text-sm font-medium text-center transition-colors ${
                  sortBy === 'mostMessages' 
                    ? 'bg-purple-100 text-purple-700 border border-purple-200' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Most Messages
              </button>
              <button 
                onClick={() => setSortBy('leastMessages')}
                className={`px-3 py-2 rounded-lg text-sm font-medium text-center transition-colors ${
                  sortBy === 'leastMessages' 
                    ? 'bg-purple-100 text-purple-700 border border-purple-200' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Least Messages
              </button>
            </div>
          )}
        </div>
        
        {filteredConversations.length === 0 ? (
          renderEmptyState()
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {sortedConversations.map(renderConversationCard)}
          </div>
        )}
      </div>

      {showDeleteAllModal && (
        <DeleteConfirmationModal
          onConfirm={handleDeleteAll}
          onCancel={() => setShowDeleteAllModal(false)}
          isDeleting={isDeleting}
        />
      )}
    </div>
  );
};

export default Conversations;