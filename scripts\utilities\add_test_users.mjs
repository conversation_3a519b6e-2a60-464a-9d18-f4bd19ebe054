// <PERSON><PERSON>t to add your profile and test users to the database
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVnZ2NjYmtoeHBlbXd1dHNxc3BoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc4MDQ5MDgsImV4cCI6MjA1MzM4MDkwOH0.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function addTestUsers() {
  console.log('🚀 Adding test users to database...');
  
  const testUsers = [
    // Your actual profile
    {
      id: '1818c8d3-c9a1-4a53-9b01-fc51e45ce55d',
      username: 'roberto.coscia.it',
      full_name: '<PERSON>',
      age: 30,
      gender: 'male',
      bio: 'Original <PERSON> from Italy. Tech enthusiast and entrepreneur.',
      created_at: new Date('2025-05-29T16:52:49Z').toISOString(),
      updated_at: new Date().toISOString()
    },
    // The other Roberto from the debug info
    {
      id: 'c6f6f365-6150-4bf2-96ee-39be2ec41094',
      username: 'roberto_coscia',
      full_name: '<PERSON> Coscia',
      age: 28,
      gender: 'male',
      bio: 'Roberto <NAME_EMAIL>. Love technology and innovation.',
      created_at: new Date('2025-05-10T19:07:10Z').toISOString(),
      updated_at: new Date().toISOString()
    },
    // Additional test users for discovery
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      username: 'roberto_martinez',
      full_name: 'Roberto Martinez',
      age: 29,
      gender: 'male',
      bio: 'Software developer from Spain. Love technology and meeting new people.',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440002',
      username: 'roberto_silva',
      full_name: 'Roberto Silva',
      age: 27,
      gender: 'male',
      bio: 'Designer and entrepreneur from Brazil. Passionate about creativity.',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: '550e8400-e29b-41d4-a716-446*********',
      username: 'alice_johnson',
      full_name: 'Alice Johnson',
      age: 28,
      gender: 'female',
      bio: 'Love hiking, reading, and good coffee. Looking for meaningful connections.',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440004',
      username: 'bob_smith',
      full_name: 'Bob Smith',
      age: 32,
      gender: 'male',
      bio: 'Software engineer by day, chef by night. Enjoy traveling and trying new cuisines.',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ];
  
  try {
    console.log('📝 Inserting test users...');
    
    for (const user of testUsers) {
      console.log(`   Adding: ${user.full_name} (${user.username})`);
      
      const { data, error } = await supabase
        .from('profiles')
        .upsert(user, { 
          onConflict: 'id',
          ignoreDuplicates: false 
        })
        .select()
        .single();
      
      if (error) {
        console.log(`   ❌ Error adding ${user.full_name}:`, error.message);
      } else {
        console.log(`   ✅ Successfully added ${user.full_name}`);
      }
    }
    
    // Verify the insert
    console.log('\n🔍 Verifying inserted users...');
    const { data: allProfiles, error: fetchError } = await supabase
      .from('profiles')
      .select('id, username, full_name, age, gender')
      .order('created_at', { ascending: false });
    
    if (fetchError) {
      console.log('❌ Verification failed:', fetchError);
    } else {
      console.log(`✅ Total profiles in database: ${allProfiles?.length || 0}`);
      
      if (allProfiles && allProfiles.length > 0) {
        allProfiles.forEach((profile, index) => {
          console.log(`   #${index + 1}: ${profile.full_name} (@${profile.username}) - Age: ${profile.age}, Gender: ${profile.gender}`);
        });
      }
    }
    
    console.log('\n🎉 Database setup complete! You can now test user discovery.');
    
  } catch (error) {
    console.error('❌ Failed to add test users:', error);
  }
}

addTestUsers();
