ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS age INTEGER,
ADD COLUMN IF NOT EXISTS gender TEXT,
ADD COLUMN IF NOT EXISTS is_discoverable BOOLEAN DEFAULT TRUE;

COMMENT ON COLUMN public.profiles.age IS 'Age of the user';
COMMENT ON COLUMN public.profiles.gender IS 'Gender identity of the user';
COMMENT ON COLUMN public.profiles.is_discoverable IS 'Whether the profile can be shown in discovery results';
