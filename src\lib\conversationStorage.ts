/*
 * conversationStorage.ts – refactored May 30 2025 (patch 4)
 * --------------------------------------------------------
 * Fixes:
 *   • Removed reactions field from message inserts (column doesn't exist)
 *   • Removed timestamp and status fields from message inserts (columns don't exist)
 *   • Fixed sender_id/twin_id logic to match actual schema
 *   • Updated fetchMessagesForConversation to handle missing fields
 */

// ────────────────────────────────────────────────────────────────────────────────
// Imports
// ────────────────────────────────────────────────────────────────────────────────
import {
  Conversation as <PERSON><PERSON>onversation,
  Profile,
  DigitalTwin as <PERSON>Digi<PERSON><PERSON><PERSON>,
} from "../types";
import { supabase } from "./supabase";
import { ErrorLogger } from "./errorHandler";

const captureError = (error: unknown) => {
  if (typeof ErrorLogger.logError === "function") {
    ErrorLogger.logError(error as Error, "database", "Error captured");
  } else {
    console.error(error);
  }
};

// ────────────────────────────────────────────────────────────────────────────────
// Local type extensions – bring the domain model in sync with the DB schema
// ────────────────────────────────────────────────────────────────────────────────
export interface DigitalTwin extends BaseDigitalTwin {
  owner_id?: string;
  description?: string | null;
  is_public?: boolean;
  training_complete?: boolean;
  model_parameters?: any | null;
  created_at?: string;
  updated_at?: string;
}

export interface Conversation extends BaseConversation {
  /** id of the signed-in user owning this record */
  userId: string;
  /** twin id if this is a twin conversation */
  twinId?: string;
  /** UI title */
  title?: string | null;
  /** summary - not in DB schema but kept for compatibility */
  summary?: string | null;
  /** status of the conversation */
  status?: string;
}

// ────────────────────────────────────────────────────────────────────────────────
// Fetch conversations for the signed-in user
// ────────────────────────────────────────────────────────────────────────────────
export async function fetchUserConversations(currentUserId: string): Promise<Conversation[]> {
  try {
    // Fetch conversations where user is owner or participant
    const { data: ownerRows, error: ownerError } = await supabase
      .from("conversations")
      .select(
        `*,
        digital_twins (
          id,
          name,
          owner_id,
          description,
          is_public,
          training_complete,
          model_parameters,
          created_at,
          updated_at
        )`
      )
      .eq('user_id', currentUserId);
    const { data: participantRows, error: participantError } = await supabase
      .from("conversations")
      .select(
        `*,
        digital_twins (
          id,
          name,
          owner_id,
          description,
          is_public,
          training_complete,
          model_parameters,
          created_at,
          updated_at
        )`
      )
      .eq('participant_user_id', currentUserId);
    if (ownerError) captureError(ownerError);
    if (participantError) captureError(participantError);
    const rows = [...(ownerRows || []), ...(participantRows || [])];
    // Deduplicate by id
    const uniqueRows = Object.values(Object.fromEntries(rows.map(r => [r.id, r])));
    if (!uniqueRows) return [];
    const rawConversations: (Conversation | null)[] = await Promise.all(
      uniqueRows.map(async (row: any): Promise<Conversation | null> => {
        let digitalTwin: DigitalTwin | undefined;
        let realUser: Profile | undefined;
        let type: "twin" | "real_user" = row.twin_id ? "twin" : "real_user";
        if (row.twin_id) {
          const twin = Array.isArray(row.digital_twins) ? row.digital_twins[0] : row.digital_twins;
          if (twin) {
            digitalTwin = {
              id: twin.id,
              name: twin.name,
              owner_id: twin.owner_id,
              description: twin.description,
              is_public: twin.is_public,
              training_complete: twin.training_complete,
              model_parameters: twin.model_parameters,
              created_at: twin.created_at,
              updated_at: twin.updated_at,
              personalityTraits: [],
              interests: [],
              avatar: undefined
            };
          }
        }
        // For real user, fetch profile if needed (optional: can be optimized)
        if (row.participant_user_id && type === 'real_user') {
          const { data: profile } = await supabase.from('profiles').select('*').eq('id', row.participant_user_id).single();
          if (profile) realUser = profile;
        }
        return {
          id: row.id,
          userId: row.user_id,
          twinId: row.twin_id ?? undefined,
          digitalTwin,
          realUser,
          createdAt: new Date(row.created_at),
          lastActive: new Date(row.updated_at),
          messages: [],
          engagementScore: row.engagement_score ?? 0,
          type,
          title: row.title ?? null,
          summary: row.summary ?? null,
          status: row.status ?? "active",
        };
      })
    );
    const conversations = rawConversations.filter(Boolean) as Conversation[];
    return conversations;
  } catch (err) {
    captureError(err);
    return [];
  }
}

// ────────────────────────────────────────────────────────────────────────────────
// saveConversation - Enhanced with auto-creation for demo twins
// ────────────────────────────────────────────────────────────────────────────────
export async function saveConversation(conversation: Conversation, userId: string) {
  const isTwin = conversation.type === 'twin';
  // Always set a title: use participant name or fallback
  let title = conversation.title;
  if (!title) {
    if (isTwin && conversation.digitalTwin?.name) title = conversation.digitalTwin.name;
    else if (!isTwin && conversation.realUser?.name) title = conversation.realUser.name;
    else title = 'Conversation';
  }
  
  // Defensive checks for date properties
  const createdAt = conversation.createdAt || new Date();
  const lastActive = conversation.lastActive || new Date();
  const createdAtISO = createdAt instanceof Date ? createdAt.toISOString() : new Date(createdAt).toISOString();
  const lastActiveISO = lastActive instanceof Date ? lastActive.toISOString() : new Date(lastActive).toISOString();
  
  const upsertData: any = {
    id: conversation.id,
    user_id: userId,
    twin_id: isTwin ? conversation.digitalTwin?.id : null,
    participant_user_id: !isTwin ? conversation.realUser?.id : null,
    created_at: createdAtISO,
    last_active: lastActiveISO,
    engagement_score: conversation.engagementScore,
    type: conversation.type,
    title,
    status: conversation.status ?? 'active'
  };
  Object.keys(upsertData).forEach(
    (k) => upsertData[k] === undefined && delete upsertData[k]
  );
  const { error } = await supabase
    .from('conversations')
    .upsert(upsertData);
  if (error) captureError(error);
}

// ────────────────────────────────────────────────────────────────────────────────
// fetchMessagesForConversation - updated to handle missing fields
// ────────────────────────────────────────────────────────────────────────────────
export async function fetchMessagesForConversation(conversationId: string) {
  try {
    const { data: messages, error } = await supabase
      .from("messages")
      .select(`*`)
      .eq("conversation_id", conversationId)
      .order("created_at", { ascending: true });
    if (error) throw error;
    return messages.map((m) => ({
      id: m.id,
      conversationId: m.conversation_id,
      senderId: m.sender_id,
      content: m.content,
      createdAt: new Date(m.created_at),
      updatedAt: new Date(m.updated_at),
      status: m.status ?? "sent",
    }));
  } catch (err) {
    captureError(err);
    return [];
  }
}

// ────────────────────────────────────────────────────────────────────────────────
// saveMessage - updated to handle missing fields
// ────────────────────────────────────────────────────────────────────────────────
export async function saveMessage(message: any) {
  try {
    // Use timestamp for both created_at and updated_at since Message type only has timestamp
    const timestamp = message.timestamp || message.createdAt || new Date();
    const timestampISO = timestamp instanceof Date ? timestamp.toISOString() : new Date(timestamp).toISOString();
    
    const { error } = await supabase
      .from("messages")
      .upsert({
        id: message.id,
        conversation_id: message.conversationId,
        sender_id: message.senderId,
        content: message.content,
        created_at: timestampISO,
        updated_at: timestampISO,
        status: message.status ?? "sent",
      });
    if (error) throw error;
  } catch (err) {
    captureError(err);
  }
}

// ────────────────────────────────────────────────────────────────────────────────
// deleteMessage - updated to handle missing fields
// ────────────────────────────────────────────────────────────────────────────────
export async function deleteMessage(messageId: string) {
  try {
    const { error } = await supabase
      .from("messages")
      .delete()
      .eq("id", messageId);
    if (error) throw error;
  } catch (err) {
    captureError(err);
  }
}

// ────────────────────────────────────────────────────────────────────────────────
// Additional helper functions (if any) – to be defined here
// ────────────────────────────────────────────────────────────────────────────────