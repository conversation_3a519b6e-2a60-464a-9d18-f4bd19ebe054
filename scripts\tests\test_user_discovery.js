// Test script to verify user discovery functionality
// This script can be run in browser console or as a Node.js script

// Mock test data for the two <PERSON> accounts
const testUsers = [
  {
    email: '<EMAIL>',
    name: '<PERSON>',
    id: 'user1-mock-id'
  },
  {
    email: 'tecky<PERSON><PERSON>@gmail.com', 
    name: '<PERSON>',
    id: 'user2-mock-id'
  }
];

console.log('🧪 Testing user discovery scenario:');
console.log('User 1:', testUsers[0]);
console.log('User 2:', testUsers[1]);

// Test the browseProfiles function logic
function testBrowseProfilesLogic(currentUserId, allUsers) {
  console.log(`\n🔍 Testing browseProfiles for user: ${currentUserId}`);
  
  // This simulates the query: .neq('id', currentUserId)
  const otherUsers = allUsers.filter(user => user.id !== currentUserId);
  
  console.log(`📊 Results:
  - Total users in database: ${allUsers.length}
  - Current user ID: ${currentUserId}
  - Other users (should be discoverable): ${otherUsers.length}
  - Other users: ${JSON.stringify(otherUsers, null, 2)}
  `);
  
  return otherUsers;
}

// Test both scenarios
console.log('\n=== TEST SCENARIO 1: User 1 browsing ===');
testBrowseProfilesLogic(testUsers[0].id, testUsers);

console.log('\n=== TEST SCENARIO 2: User 2 browsing ===');
testBrowseProfilesLogic(testUsers[1].id, testUsers);

console.log('\n✅ Expected behavior: Each user should see the other user in their discovery results');
console.log('❌ Current issue: Users might only see themselves');
console.log('\n🔧 Fix applied: Removed non-existent is_discoverable filter from browseProfiles query');
