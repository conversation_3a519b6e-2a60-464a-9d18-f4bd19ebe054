import React, { useState } from 'react';
import { Trash2, AlertCircle } from 'lucide-react';
import { useToast } from '../../hooks/useToast';

interface ConversationActionsProps {
  onDelete: () => Promise<void>;
  showConfirmation?: boolean;
}

export const ConversationActions: React.FC<ConversationActionsProps> = ({
  onDelete,
  showConfirmation = true
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const { showSuccess, showError } = useToast();

  const handleDelete = async () => {
    if (showConfirmation && !showDeleteConfirm) {
      setShowDeleteConfirm(true);
      return;
    }

    setIsDeleting(true);
    try {
      await onDelete();
      showSuccess('Conversation deleted successfully');
    } catch (error) {
      showError('Failed to delete conversation');
      console.error('Delete error:', error);
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  return (
    <div className="relative">
      {showDeleteConfirm ? (
        <div className="absolute right-0 bottom-full mb-2 w-64 bg-white rounded-lg shadow-lg p-4 animate-fadeIn">
          <div className="flex items-start mb-3">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2 flex-shrink-0" />
            <p className="text-sm text-gray-700">Are you sure you want to delete this conversation?</p>
          </div>
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setShowDeleteConfirm(false)}
              className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              disabled={isDeleting}
              className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </button>
          </div>
        </div>
      ) : (
        <button
          onClick={handleDelete}
          className="text-red-500 hover:text-red-600 p-2 rounded-full hover:bg-red-50 transition-colors"
          title="Delete conversation"
        >
          <Trash2 className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};