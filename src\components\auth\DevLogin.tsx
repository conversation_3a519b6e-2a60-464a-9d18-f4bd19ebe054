import React, { useState } from 'react';
import { supabase } from '../../lib/supabase';
import { useNavigate } from 'react-router-dom';

interface DevLoginProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const DevLogin: React.FC<DevLoginProps> = ({ onSuccess, onError }) => {
  const [selectedUser, setSelectedUser] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const testUsers = [
    { id: '550e8400-e29b-41d4-a716-446655440006', email: '<EMAIL>', name: 'Test User' },
    { id: '550e8400-e29b-41d4-a716-446655440001', email: '<EMAIL>', name: '<PERSON>' },
    { id: '550e8400-e29b-41d4-a716-446655440002', email: '<EMAIL>', name: '<PERSON>' },
    { id: '550e8400-e29b-41d4-a716-446655440003', email: '<EMAIL>', name: '<PERSON> <PERSON>' },
    { id: '550e8400-e29b-41d4-a716-446655440004', email: '<EMAIL>', name: 'David <PERSON>' },
    { id: '550e8400-e29b-41d4-a716-446655440005', email: '<EMAIL>', name: 'Emma <PERSON>' },
  ];

  const handleDevLogin = async () => {
    if (!selectedUser) {
      onError?.('Please select a user');
      return;
    }

    setIsLoading(true);
    try {
      const user = testUsers.find(u => u.id === selectedUser);
      if (!user) {
        throw new Error('User not found');
      }      // Sign in with email and password for local development
      const { error } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: 'password123'
      });

      if (error) throw error;

      onSuccess?.();
      navigate('/dashboard');
    } catch (error) {
      console.error('Dev login error:', error);
      onError?.(error instanceof Error ? error.message : 'Failed to login');
    } finally {
      setIsLoading(false);
    }
  };

  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <h3 className="text-sm font-medium text-yellow-800 mb-3">Development Login</h3>
      <select
        value={selectedUser}
        onChange={(e) => setSelectedUser(e.target.value)}
        className="w-full mb-3 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        disabled={isLoading}
      >
        <option value="">Select a test user...</option>
        {testUsers.map((user) => (
          <option key={user.id} value={user.id}>
            {user.name} ({user.email})
          </option>
        ))}
      </select>
      <button
        onClick={handleDevLogin}
        disabled={isLoading || !selectedUser}
        className="w-full px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? 'Logging in...' : 'Login as Test User'}
      </button>
    </div>
  );
};
