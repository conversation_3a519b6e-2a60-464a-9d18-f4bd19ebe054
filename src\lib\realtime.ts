import { RealtimeChannel, RealtimeChannelOptions } from '@supabase/supabase-js';
import { supabase } from './supabase';

interface RealtimeConfig {
  maxRetries?: number;
  retryInterval?: number;
  debug?: boolean;
}

class RealtimeManager {
  private channels: Map<string, RealtimeChannel> = new Map();
  private config: Required<RealtimeConfig>;
  private retryAttempts: Map<string, number> = new Map();

  constructor(config: RealtimeConfig = {}) {
    this.config = {
      maxRetries: config.maxRetries ?? 5,
      retryInterval: config.retryInterval ?? 2000,
      debug: config.debug ?? false
    };
  }

  private log(...args: any[]) {
    if (this.config.debug) {
      console.log('[RealtimeManager]', ...args);
    }
  }

  async subscribe(
    channelName: string,
    options: RealtimeChannelOptions = {}
  ): Promise<RealtimeChannel | null> {
    try {
      // Check if channel already exists
      if (this.channels.has(channelName)) {
        return this.channels.get(channelName) ?? null;
      }

      const channel = supabase.channel(channelName, options)
        .on('system', ({ event, status }) => {
          this.log(`System event: ${event}`, status);
        })
        .on('error', (error) => {
          this.log('Channel error:', error);
          this.handleError(channelName, error);
        });

      channel.subscribe(async (status) => {
        this.log(`Subscription status:`, status);

        if (status === 'SUBSCRIBED') {
          this.retryAttempts.delete(channelName);
        } else if (status === 'CLOSED' || status === 'CHANNEL_ERROR') {
          await this.handleReconnect(channelName, options);
        }
      });

      this.channels.set(channelName, channel);
      return channel;
    } catch (error) {
      this.log('Subscription error:', error);
      return null;
    }
  }

  private async handleReconnect(
    channelName: string,
    options: RealtimeChannelOptions
  ) {
    const attempts = this.retryAttempts.get(channelName) ?? 0;

    if (attempts >= this.config.maxRetries) {
      this.log(`Max retry attempts reached for channel: ${channelName}`);
      this.channels.delete(channelName);
      this.retryAttempts.delete(channelName);
      return;
    }

    this.retryAttempts.set(channelName, attempts + 1);
    this.log(`Attempting to reconnect channel: ${channelName}, attempt: ${attempts + 1}`);

    await new Promise(resolve => setTimeout(resolve, this.config.retryInterval));
    await this.subscribe(channelName, options);
  }

  private async handleError(channelName: string, error: any) {
    this.log(`Error in channel ${channelName}:`, error);

    const channel = this.channels.get(channelName);
    if (channel) {
      try {
        await channel.unsubscribe();
      } catch (unsubError) {
        this.log('Error unsubscribing from channel:', unsubError);
      }
    }

    this.channels.delete(channelName);
  }

  async unsubscribe(channelName: string) {
    const channel = this.channels.get(channelName);
    if (channel) {
      try {
        await channel.unsubscribe();
        this.channels.delete(channelName);
        this.retryAttempts.delete(channelName);
      } catch (error) {
        this.log('Error unsubscribing from channel:', error);
      }
    }
  }

  async unsubscribeAll() {
    const channelNames = Array.from(this.channels.keys());
    await Promise.all(channelNames.map(name => this.unsubscribe(name)));
  }
}

// Create and export a singleton instance
export const realtimeManager = new RealtimeManager({ debug: true });

// Helper function to verify WebSocket connectivity
export const verifyWebSocketConnection = async (): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const channel = await realtimeManager.subscribe('connection_test');
    if (!channel) {
      throw new Error('Failed to create WebSocket channel');
    }

    // Wait for connection or timeout
    const result = await Promise.race([
      new Promise<boolean>((resolve) => {
        channel.on('system', ({ event }) => {
          if (event === 'connected') {
            resolve(true);
          }
        });
      }),
      new Promise<boolean>((_, reject) => {
        setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 5000);
      }),
    ]);

    await realtimeManager.unsubscribe('connection_test');
    return { success: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown WebSocket error'
    };
  }
};