import React, { useState } from 'react';
import { useProfile } from '../context/ProfileContext';
import { useToast } from '../hooks/useToast';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { Heart, Bell, Lock, Shield, Trash2, UploadCloud, Settings as SettingsIcon, User, MessageCircle } from 'lucide-react'; // Organized imports
import { AvatarPicker } from '../components/AvatarPicker';
import ContactWillingnessToggle from '../components/tagging/ContactWillingnessToggle';
import ConversationBoundariesManager from '../components/settings/ConversationBoundariesManager';

// Note: useAuth might not be directly needed if useProfile provides all necessary user info and refresh capabilities

const Settings: React.FC = () => {
  const { profile, updateProfile, refreshProfile } = useProfile(); // Added refreshProfile
  const { showSuccess, showError, showLoading, dismiss } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [isBoundariesModalOpen, setIsBoundariesModalOpen] = useState(false);

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p>Please create a profile first</p>
      </div>
    );
  }

  const handleSave = async () => {
    setIsSaving(true);
    const loadingToast = showLoading('Saving changes...');

    try {
      await updateProfile(profile);
      dismiss(loadingToast);
      showSuccess('Settings saved successfully!');
    } catch (error) {
      dismiss(loadingToast);
      showError('Failed to save settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <SettingsIcon className="w-6 h-6 text-purple-500 mr-2" />
          Settings
        </h1>

        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
          <div className="bg-gradient-to-r from-purple-600 to-pink-500 px-6 py-4 flex items-center">
            {profile.avatar_url ? (
              <img src={profile.avatar_url} alt={profile.name} className="w-16 h-16 rounded-full mr-4 object-cover" />
            ) : (
              <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center text-purple-600 text-2xl font-bold mr-4">
                {profile.name?.charAt(0)?.toUpperCase() || '?'}
              </div>
            )}
            <div className="text-white">
              <h2 className="text-xl font-semibold">{profile.name}</h2>
              <p className="text-white/80">Member since {new Date(profile.createdAt).toLocaleDateString()}</p>
            </div>
          </div>

          <div className="p-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2 flex items-center">
                <UploadCloud className="w-5 h-5 text-purple-500 mr-2" />
                Profile Avatar
              </h3>
              <AvatarPicker onUploadSuccess={async () => {
                if (refreshProfile) {
                  await refreshProfile();
                  showSuccess('Avatar updated successfully!');
                }
              }} />
              <p className="text-xs text-gray-500 mt-1">Upload a PNG or JPEG, max 2MB.</p>
            </div>
            <div className="border-t border-gray-200 my-6"></div> {/* Added divider */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <User className="w-5 h-5 text-purple-500 mr-2" />
                  Profile Settings
                </h3>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="profileName" className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                    <input
                      id="profileName"
                      type="text"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      value={profile.name}
                      onChange={(e) => updateProfile({ name: e.target.value })} // updateProfile should handle this partial update
                    />
                  </div>
                  <div>
                    <label htmlFor="profileBio" className="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                    <textarea
                      id="profileBio"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      rows={4}
                      value={profile.bio || ''} // Ensure value is not null
                      onChange={(e) => updateProfile({ bio: e.target.value })} // updateProfile should handle this partial update
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <Heart className="w-5 h-5 text-purple-500 mr-2" />
                  Matching Preferences
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Age Range</label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="number"
                        className="w-24 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        placeholder="Min"
                        min="18"
                        max="100"
                      />
                      <span className="text-gray-500">to</span>
                      <input
                        type="number"
                        className="w-24 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        placeholder="Max"
                        min="18"
                        max="100"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                      <input type="checkbox" className="rounded text-purple-600 mr-2" />
                      Show only high compatibility matches
                    </label>
                  </div>
                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                      <input type="checkbox" className="rounded text-purple-600 mr-2" />
                      Notify me of new matches
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200 mt-8 pt-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <Bell className="w-5 h-5 text-purple-500 mr-2" />
                Notification Settings
              </h3>
              <div className="space-y-3">
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <input type="checkbox" className="rounded text-purple-600 mr-2" defaultChecked />
                  New messages
                </label>
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <input type="checkbox" className="rounded text-purple-600 mr-2" defaultChecked />
                  Conversation milestones
                </label>
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <input type="checkbox" className="rounded text-purple-600 mr-2" defaultChecked />
                  New matching suggestions
                </label>
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <input type="checkbox" className="rounded text-purple-600 mr-2" />
                  Marketing updates
                </label>
              </div>
            </div>

            <div className="border-t border-gray-200 mt-8 pt-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <Lock className="w-5 h-5 text-purple-500 mr-2" />
                Privacy Settings
              </h3>
              <div className="space-y-3">
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <input type="checkbox" className="rounded text-purple-600 mr-2" defaultChecked />
                  Show my profile to other users
                </label>
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <input type="checkbox" className="rounded text-purple-600 mr-2" defaultChecked />
                  Allow my interests to be used for matching
                </label>



                <div className="pt-4">
                  <button
                    onClick={() => setIsBoundariesModalOpen(true)}
                    className="flex items-center text-red-600 hover:text-red-700 transition-colors"
                  >
                    <Shield className="w-4 h-4 mr-1" />
                    Manage conversation boundaries
                  </button>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200 mt-8 pt-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <MessageCircle className="w-5 h-5 text-purple-500 mr-2" />
                Contact Availability
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Control when you're open to being contacted by other users. When enabled, users who have shown interest in you will be notified.
              </p>
              <ContactWillingnessToggle />
            </div>

            <div className="border-t border-gray-200 mt-8 pt-8 flex justify-between">
              <button 
                className={`px-5 py-2 bg-purple-600 text-white font-medium rounded-lg transition duration-200 flex items-center ${
                  isSaving ? 'opacity-75 cursor-not-allowed' : 'hover:bg-purple-700'
                }`}
                onClick={handleSave}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <LoadingSpinner size="sm" color="text-white" />
                    <span className="ml-2">Saving...</span>
                  </>
                ) : (
                  'Save Changes'
                )}
              </button>
              <button className="flex items-center text-red-600 hover:text-red-700">
                <Trash2 className="w-4 h-4 mr-1" />
                Delete Account
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Conversation Boundaries Modal */}
      <ConversationBoundariesManager
        isOpen={isBoundariesModalOpen}
        onClose={() => setIsBoundariesModalOpen(false)}
      />
    </div>
  );
};

export default Settings;