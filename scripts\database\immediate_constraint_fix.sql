-- IMMEDIATE FIX for user_interest_tags constraint violation
-- This script will definitely fix the constraint error you're seeing

-- Step 1: Drop the problematic constraint
ALTER TABLE user_interest_tags DROP CONSTRAINT IF EXISTS user_interest_tags_tag_type_check;

-- Step 2: Add the correct constraint with all 4 tag types
ALTER TABLE user_interest_tags ADD CONSTRAINT user_interest_tags_tag_type_check 
CHECK (tag_type IN ('interested', 'very_interested', 'potential_match', 'willing_to_contact'));

-- Step 3: Verify the constraint was added
-- (This will show the new constraint in the output)
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(c.oid) as constraint_definition
FROM pg_constraint c
JOIN pg_class t ON c.conrelid = t.oid
WHERE t.relname = 'user_interest_tags'
AND contype = 'c'
AND conname = 'user_interest_tags_tag_type_check';
