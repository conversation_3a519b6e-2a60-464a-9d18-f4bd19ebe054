-- Add personality test columns to profiles table
-- This script adds Big Five personality test scoring columns

-- Add personality test result columns
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS extraversion_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS agreeableness_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS conscientiousness_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS neuroticism_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS openness_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS personality_test_completed_at TIMESTAMPTZ DEFAULT NULL,
ADD COLUMN IF NOT EXISTS personality_insights JSONB DEFAULT NULL;

-- Add comments for clarity
COMMENT ON COLUMN public.profiles.extraversion_score IS 'Big Five Extraversion score (0-100)';
COMMENT ON COLUMN public.profiles.agreeableness_score IS 'Big Five Agreeableness score (0-100)';
COMMENT ON COLUMN public.profiles.conscientiousness_score IS 'Big Five Conscientiousness score (0-100)';
COMMENT ON COLUMN public.profiles.neuroticism_score IS 'Big Five Neuroticism score (0-100)';
COMMENT ON COLUMN public.profiles.openness_score IS 'Big Five Openness score (0-100)';
COMMENT ON COLUMN public.profiles.personality_test_completed_at IS 'Timestamp when personality test was last completed';
COMMENT ON COLUMN public.profiles.personality_insights IS 'JSON object containing personality insights and interpretations';

-- Create index for personality test queries
CREATE INDEX IF NOT EXISTS profiles_personality_test_idx ON public.profiles (personality_test_completed_at) WHERE personality_test_completed_at IS NOT NULL;

SELECT 'Personality test columns added successfully' as status;
