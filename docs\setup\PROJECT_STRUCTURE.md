# SoulTwinSync Project Structure

This document outlines the organized folder structure of the SoulTwinSync project.

## 📁 Folder Organization

### `/scripts/` - Utility Scripts and Database Management
- **`/database/`** - SQL scripts for database setup and fixes
  - Database schema creation and migration files
  - RLS (Row Level Security) policy fixes
  - Test data creation scripts
  - All `.sql` files

- **`/tests/`** - Test scripts and verification utilities
  - Database testing scripts
  - User discovery tests
  - Tagging system tests
  - All `*test*.js` files

- **`/utilities/`** - Utility scripts and maintenance tools
  - Database check and diagnostic scripts
  - Data management utilities
  - All `.mjs` files and utility `.js` files

### `/docs/` - Documentation
- **`/database/`** - Database-related documentation
  - Setup instructions
  - Fix procedures
  - Schema documentation
  - All database `.md` files

- **`/setup/`** - Project setup and configuration docs
  - This project structure document
  - Installation guides
  - Configuration instructions

### `/src/` - Application Source Code
- React TypeScript application code
- Components, pages, hooks, and utilities
- Main application logic

### `/supabase/` - Supabase Configuration
- Database migrations
- Configuration files
- Supabase-specific setup

## 🚀 Quick Start

1. **Database Setup**: Run SQL scripts from `/scripts/database/`
2. **Testing**: Use scripts from `/scripts/tests/` to verify functionality
3. **Utilities**: Use `/scripts/utilities/` for maintenance and diagnostics
4. **Documentation**: Check `/docs/` for detailed guides

## 📋 Key Files

- **`FINAL_WORKING_FIX.sql`** - Main database setup script (in `/scripts/database/`)
- **`start-dev.bat`** - Development server startup script (root level)
- **`package.json`** - Node.js dependencies and scripts (root level)

## 🔧 Development Workflow

1. Apply database fixes from `/scripts/database/FINAL_WORKING_FIX.sql`
2. Run `start-dev.bat` to start the development server
3. Use test scripts from `/scripts/tests/` to verify functionality
4. Refer to documentation in `/docs/` for guidance

This organized structure makes the project more maintainable and easier to navigate! ✨
