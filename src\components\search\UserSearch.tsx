import React, { useState } from 'react';
import { Search, Users } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { Profile } from '../../types';
import { searchUsers } from '../../lib/profile';

interface UserSearchProps {
  onUserSelect?: (user: Profile) => void;
}

export const UserSearch: React.FC<UserSearchProps> = ({ onUserSelect }) => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Profile[]>([]);
  const [totalUsers, setTotalUsers] = useState<number>(0);
  const [isSearching, setIsSearching] = useState(false);

  const handleSearch = async (term: string) => {
    setSearchTerm(term);
    if (term.length < 2) {
      setSearchResults([]);
      return;
    }    setIsSearching(true);
    try {
      const { users, total } = await searchUsers(term, user?.id);
      setSearchResults(users);
      setTotalUsers(total);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
          <Users className="h-4 w-4" />
          <span>{totalUsers} users registered</span>
        </div>
        
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            placeholder="Search users by name..."
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
      </div>

      {isSearching ? (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500 mx-auto"></div>
        </div>
      ) : searchResults.length > 0 ? (
        <div className="space-y-2">
          {searchResults.map((user) => (
            <div
              key={user.id}
              className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition cursor-pointer"
              onClick={() => onUserSelect?.(user)}
            >
              <div className="flex items-center">
                {user.avatar_url ? (
                  <img
                    src={user.avatar_url}
                    alt={user.name}
                    className="w-12 h-12 rounded-full object-cover mr-4"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold mr-4">
                    {user.name.charAt(0)}
                  </div>
                )}
                <div>
                  <h3 className="font-semibold text-gray-800">{user.name}</h3>
                  <p className="text-sm text-gray-500">{user.bio}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : searchTerm.length > 0 && (
        <div className="text-center py-4 text-gray-500">
          No users found matching "{searchTerm}"
        </div>
      )}
    </div>
  );
};