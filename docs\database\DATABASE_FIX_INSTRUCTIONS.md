# Database Fix Instructions for SOULSYNK Tagging System

## Issues Identified
1. **Check constraint violation**: The `user_interest_tags_tag_type_check` constraint only allows 3 tag types, but the code uses 4
2. **Row-level security policy violation**: RLS policies are blocking inserts
3. **Foreign key reference mismatch**: Code assumes `auth.users(id)` but database uses `profiles(id)`

## Database Changes Required

### Step 1: Apply Database Schema Fixes
You need to run the SQL script `fix_tagging_database_issues.sql` in your Supabase database console.

**How to apply:**
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the contents of `fix_tagging_database_issues.sql`
4. Click **Run** to execute

### Step 2: Verify the Fix
After applying the SQL script, test the functionality:

1. **Open the app**: http://localhost:5175/
2. **Log in** with a valid user account
3. **Navigate to the Discover page**
4. **Try using the tagging buttons**:
   - Interest tagging buttons (💫, ⭐, 💖)
   - Contact willingness toggle
   - Check if notifications are created

### Step 3: Run Test Script (Optional)
You can also run the test script in the browser console:
1. Open browser DevTools (F12)
2. Go to Console tab
3. Paste the contents of `test_tagging_system_fixes.js`
4. Run `testTaggingSystem()` to check database connectivity

## What the SQL Fix Does

### 1. Updates Check Constraints
```sql
-- Allows all 4 tag types including 'willing_to_contact'
ALTER TABLE user_interest_tags ADD CONSTRAINT user_interest_tags_tag_type_check 
CHECK (tag_type IN ('interested', 'very_interested', 'potential_match', 'willing_to_contact'));
```

### 2. Fixes Foreign Key References
```sql
-- Ensures foreign keys point to profiles table (not auth.users)
ALTER TABLE user_interest_tags ADD CONSTRAINT user_interest_tags_tagger_id_fkey 
FOREIGN KEY (tagger_id) REFERENCES profiles(id) ON DELETE CASCADE;
```

### 3. Updates Unique Constraints
```sql
-- Allows multiple tag types per user pair
ALTER TABLE user_interest_tags ADD CONSTRAINT user_interest_tags_tagger_id_tagged_user_id_tag_type_key 
UNIQUE(tagger_id, tagged_user_id, tag_type);
```

## Current Application Status

✅ **Completed Fixes:**
- Debug modal removed from Discover page
- Import errors fixed in NotificationPanel.tsx
- ContactWillingnessToggle.tsx TypeScript warnings resolved
- notifications.ts file corruption fixed

🔄 **Pending Database Fixes:**
- Apply `fix_tagging_database_issues.sql` to Supabase
- Test tagging system functionality
- Verify notifications are created

## Testing Checklist

After applying the database fixes, verify:

- [ ] Can click interest tag buttons without errors
- [ ] Can toggle contact willingness status
- [ ] Tags appear in user badges
- [ ] Notifications are created for tagging events
- [ ] No console errors when using tagging features
- [ ] RLS policies allow proper read/write access

## Troubleshooting

If you still get errors after applying the SQL fix:

1. **Constraint violations**: Check that the SQL script ran completely
2. **RLS policy errors**: Verify user is authenticated and has valid profile
3. **Foreign key errors**: Ensure the user's profile exists in the `profiles` table

## Next Steps

1. Apply the SQL script to fix database constraints
2. Test the tagging functionality in the app
3. If everything works, the SOULSYNK tagging system should be fully functional!

The development server is running at: **http://localhost:5175/**
