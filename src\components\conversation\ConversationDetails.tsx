import React, { useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Bot, Info, User } from 'lucide-react';

import { useConversation } from '../../context/ConversationContext';
import { useProfile } from '../../context/ProfileContext';
import { useToast } from '../../hooks/useToast';

import ChatMessage from './ChatMessage';
import ChatControls from './ChatControls';
import TwinProfile from './TwinProfile';
import UserProfile from './UserProfile';
import SwitchUserModal from './SwitchUserModal';
import { LoadingSpinner } from '../ui/LoadingSpinner';

/**
 * ConversationDetails – single‑chat view.
 * Allows users to open the profile drawer, send messages, and switch
 * between the digital‑twin persona and a real‑user chat partner.
 */
const ConversationDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { getConversation, startConversation } = useConversation();
  const { profile } = useProfile();
  const { showError } = useToast();

  const [conversation, setConversation] = useState(() =>
    id ? getConversation(id) : undefined
  );
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isSwitchOpen, setIsSwitchOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  /* -------------------------------------------------- */
  /* Load / refresh conversation                         */
  /* -------------------------------------------------- */
  useEffect(() => {
    if (!id) return;

    const current = getConversation(id);
    if (!current) {
      navigate('/conversations');
      return;
    }
    setConversation(current);
    setIsLoading(false);
  }, [id, getConversation, navigate]);

  /* -------------------------------------------------- */
  /* Auto‑scroll to newest message                      */
  /* -------------------------------------------------- */
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversation?.messages]);

  /* -------------------------------------------------- */
  /* Switch chat partner                                */
  /* -------------------------------------------------- */
  const handleSwitchUser = (type: 'twin' | 'real_user') => {
    if (!conversation) return;

    // Resolve who the next chat partner should be.
    let partner: any | undefined;
    if (type === 'twin') {
      partner = conversation.digitalTwin || conversation.realUser;
    } else {
      partner = conversation.realUser || conversation.digitalTwin;
    }

    if (!partner) {
      showError('Cannot switch chat partner – no participant data');
      return;
    }

    try {
      (async () => {
        const newConversation = await startConversation(partner, type);
        setIsSwitchOpen(false);
        if (newConversation && newConversation.id) {
          navigate(`/conversations/${newConversation.id}`);
        } else {
          showError('Failed to switch chat partner');
        }
      })();
    } catch (error) {
      console.error('Error switching chat partner:', error);
      showError('Failed to switch chat partner');
    }
  };

  /* -------------------------------------------------- */
  /* Early‑return states                                */
  /* -------------------------------------------------- */
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!conversation) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p>Conversation not found</p>
      </div>
    );
  }

  /* -------------------------------------------------- */
  /* Render                                             */
  /* -------------------------------------------------- */
  const participantName =
    conversation.type === 'twin'
      ? conversation.digitalTwin?.name
      : conversation.realUser?.name || 'User';
  const participantAvatar =
    conversation.type === 'twin'
      ? conversation.digitalTwin?.avatar
      : conversation.realUser?.avatar_url;

  return (
    <div className="container mx-auto px-4 py-4 h-[calc(100vh-64px-68px)] flex flex-col">
      {/* ------------------------------------------- */}
      {/* Header                                       */}
      {/* ------------------------------------------- */}
      <div className="flex items-center mb-4">
        <button
          onClick={() => navigate('/conversations')}
          className="mr-3 text-gray-600 hover:text-gray-800"
          aria-label="Back to conversations"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>

        <div
          className="flex-grow flex items-center cursor-pointer"
          onClick={() => setIsProfileOpen(true)}
        >
          {participantAvatar ? (
            <img
              src={participantAvatar}
              alt={participantName}
              className="w-10 h-10 rounded-full object-cover mr-3"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white font-bold mr-3">
              {participantName?.charAt(0) || '?'}
            </div>
          )}
          <div>
            <h1 className="font-semibold text-gray-800">{participantName}</h1>
            <p className="text-xs text-gray-500">
              {conversation.messages?.length || 0} messages • Last active{' '}
              {new Date(conversation.lastActive).toLocaleTimeString()}
            </p>
          </div>
        </div>

        {/* Button to open the switch‑modal */}
        <button
          onClick={() => setIsSwitchOpen(true)}
          className="ml-2 text-gray-600 hover:text-gray-800"
          title="Switch chat partner"
        >
          {conversation.type === 'twin' ? (
            <Bot className="h-5 w-5" />
          ) : (
            <User className="h-5 w-5" />
          )}
        </button>

        <button
          onClick={() => setIsProfileOpen(true)}
          className="ml-2 text-gray-600 hover:text-gray-800"
          aria-label="View profile"
        >
          <Info className="h-5 w-5" />
        </button>
      </div>      {/* ------------------------------------------- */}
      {/* Messages list                                 */}
      {/* ------------------------------------------- */}
      <div className="flex-grow overflow-y-auto mb-4 bg-gray-50 rounded-lg p-4 shadow-inner">
        {conversation.messages?.map((m) => (
          <ChatMessage 
            key={m.id} 
            message={m} 
            participantName={participantName}
            participantAvatar={participantAvatar}
            currentUserAvatar={profile?.avatar_url}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* ------------------------------------------- */}
      {/* Composer / controls                           */}
      {/* ------------------------------------------- */}
      <ChatControls
        conversationId={conversation.id}
        isTwinChat={conversation.type === 'twin'}
        onSwitchUser={() => setIsSwitchOpen(true)}
      />

      {/* ------------------------------------------- */}
      {/* Profile drawer                                */}
      {/* ------------------------------------------- */}
      {isProfileOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex justify-end">
          <div className="bg-white w-full max-w-md overflow-y-auto animate-slideInRight">
            <div className="sticky top-0 bg-white z-10 p-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-semibold">Profile</h2>
              <button
                onClick={() => setIsProfileOpen(false)}
                className="text-gray-500 hover:text-gray-700"
                aria-label="Close profile"
              >
                <Info className="h-6 w-6" />
              </button>
            </div>

            {conversation.type === 'twin' && conversation.digitalTwin ? (
              <TwinProfile
                digitalTwin={conversation.digitalTwin}
                onClose={() => setIsProfileOpen(false)}
              />
            ) : conversation.realUser ? (
              <UserProfile
                user={conversation.realUser}
                onClose={() => setIsProfileOpen(false)}
              />
            ) : null}
          </div>
        </div>
      )}

      {/* ------------------------------------------- */}
      {/* Switch‑user modal                             */}
      {/* ------------------------------------------- */}
      {isSwitchOpen && (
        <SwitchUserModal
          currentType={conversation.type}
          onSwitch={handleSwitchUser}
          onClose={() => setIsSwitchOpen(false)}
        />
      )}
    </div>
  );
};

export default ConversationDetails;