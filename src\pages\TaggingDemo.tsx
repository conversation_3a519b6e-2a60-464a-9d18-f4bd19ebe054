import React from 'react';
import { Heart, Users, MessageCircle } from 'lucide-react';
import { useProfile } from '../context/ProfileContext';
import InterestTagButton from '../components/tagging/InterestTagButton';
import ContactWillingnessToggle from '../components/tagging/ContactWillingnessToggle';
import UserBadges, { DetailedInterestBadges } from '../components/tagging/UserBadges';
import NotificationPanel from '../components/notifications/NotificationPanel';

/**
 * Demo page to showcase the user tagging system features
 */
const TaggingDemo: React.FC = () => {
  const { profile } = useProfile();

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p>Please create a profile first</p>
      </div>
    );
  }

  // Mock profile for demonstration
  const mockProfile = {
    id: 'demo-user-123',
    name: 'Demo User',
    age: 28,
    gender: 'Not specified',
    bio: 'This is a demo user for testing the tagging system',
    avatar_url: null,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
          User Tagging System Demo
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Your Settings */}
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <MessageCircle className="w-5 h-5 text-purple-500 mr-2" />
                Your Contact Availability
              </h2>
              <p className="text-gray-600 mb-4">
                Control when you're open to being contacted by other users.
              </p>
              <ContactWillingnessToggle showLabel={true} />
            </div>

            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <Users className="w-5 h-5 text-purple-500 mr-2" />
                Your Profile Badges
              </h2>
              <p className="text-gray-600 mb-4">
                These badges show on your profile to indicate interest from others and your availability.
              </p>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Summary Badges:</h3>
                  <UserBadges 
                    userId={profile.id} 
                    size="md" 
                    className="justify-start"
                  />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Detailed Interest Breakdown:</h3>
                  <DetailedInterestBadges 
                    userId={profile.id} 
                    size="sm" 
                    className="justify-start"
                  />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <Heart className="w-5 h-5 text-purple-500 mr-2" />
                Notifications
              </h2>
              <p className="text-gray-600 mb-4">
                Get notified when users show interest or when someone you've tagged becomes available.
              </p>
              <div className="flex justify-center">
                <NotificationPanel />
              </div>
            </div>
          </div>

          {/* Right Column - Demo Profile */}
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Demo Profile
              </h2>
              <p className="text-gray-600 mb-4">
                This is how the tagging system appears on other users' profiles.
              </p>
              
              {/* Mock Profile Card */}
              <div className="border border-gray-200 rounded-lg p-4 mb-4">
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white text-2xl font-bold mr-4">
                    {mockProfile.name.charAt(0)}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{mockProfile.name}</h3>
                    <p className="text-gray-600">{mockProfile.age} years old</p>
                  </div>
                </div>
                
                <p className="text-gray-700 mb-4">{mockProfile.bio}</p>
                
                {/* User Badges */}
                <div className="mb-4">
                  <UserBadges 
                    userId={mockProfile.id} 
                    size="sm" 
                    className="justify-start"
                  />
                </div>
                
                {/* Interest Tag Button */}
                <div className="flex justify-center">
                  <InterestTagButton 
                    profileId={mockProfile.id}
                    profileName={mockProfile.name}
                    size="md"
                  />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                How It Works
              </h2>
              <div className="space-y-4 text-sm text-gray-600">
                <div className="flex items-start">
                  <div className="w-6 h-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">
                    1
                  </div>
                  <div>
                    <strong>Tag Interest:</strong> Click the "Tag Interest" button on profiles to express your interest level.
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">
                    2
                  </div>
                  <div>
                    <strong>Set Availability:</strong> Use the contact willingness toggle to signal when you're open to conversations.
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">
                    3
                  </div>
                  <div>
                    <strong>Get Notified:</strong> When someone you've tagged becomes available, you'll receive a notification.
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">
                    4
                  </div>
                  <div>
                    <strong>View Badges:</strong> See visual indicators of interest and availability on profiles.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaggingDemo;
