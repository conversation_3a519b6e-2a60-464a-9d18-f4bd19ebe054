/*
  # Add User Twins and Chat History

  1. New Tables
    - `user_twins`: Stores digital twin configurations
    - `twin_chat_history`: Stores conversation history with twins
    - `twin_training_data`: Stores training data for twins

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
    - Ensure proper data access controls

  3. Performance
    - Add indexes for frequently queried columns
*/

-- Create user_twins table
CREATE TABLE IF NOT EXISTS user_twins (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) NOT NULL,
  personality_description text NOT NULL,
  conversation_style jsonb NOT NULL,
  interests jsonb NOT NULL,
  behavioral_settings jsonb NOT NULL,
  training_status text DEFAULT 'pending',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create twin_chat_history table
CREATE TABLE IF NOT EXISTS twin_chat_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id),
  twin_id uuid REFERENCES user_twins(id),
  message text NOT NULL,
  message_vector vector(1536),
  timestamp timestamptz DEFAULT now(),
  is_twin_message boolean DEFAULT false,
  encrypted_content text,
  encryption_key_id uuid
);

-- Create twin_training_data table
CREATE TABLE IF NOT EXISTS twin_training_data (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) NOT NULL,
  data_source text NOT NULL,
  content text NOT NULL,
  processed_status text DEFAULT 'pending',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE user_twins ENABLE ROW LEVEL SECURITY;
ALTER TABLE twin_chat_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE twin_training_data ENABLE ROW LEVEL SECURITY;

-- User twins policies
CREATE POLICY "Users can manage their twins"
  ON user_twins FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Chat history policies
CREATE POLICY "Users can manage chat history"
  ON twin_chat_history FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Training data policies
CREATE POLICY "Users can manage training data"
  ON twin_training_data FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Drop existing indexes if they exist
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'twin_chat_history_user_idx') THEN
        DROP INDEX twin_chat_history_user_idx;
    END IF;
    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'twin_chat_history_twin_idx') THEN
        DROP INDEX twin_chat_history_twin_idx;
    END IF;
    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'twin_chat_history_timestamp_idx') THEN
        DROP INDEX twin_chat_history_timestamp_idx;
    END IF;
    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'twin_training_data_user_idx') THEN
        DROP INDEX twin_training_data_user_idx;
    END IF;
END $$;

-- Create new indexes
CREATE INDEX IF NOT EXISTS twin_chat_history_user_idx_new ON twin_chat_history(user_id);
CREATE INDEX IF NOT EXISTS twin_chat_history_twin_idx_new ON twin_chat_history(twin_id);
CREATE INDEX IF NOT EXISTS twin_chat_history_timestamp_idx_new ON twin_chat_history(timestamp);
CREATE INDEX IF NOT EXISTS twin_training_data_user_idx_new ON twin_training_data(user_id);