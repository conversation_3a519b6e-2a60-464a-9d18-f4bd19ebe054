#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

console.log('🚀 Applying SoulTwinSync database fixes...');

// Simple approach: read the SQL files and output instructions
const scripts = [
  {
    file: './complete_fix.sql',
    description: 'Create missing tables and add test users'
  },
  {
    file: './fix_tagging_rls_final.sql', 
    description: 'Fix RLS policies for tagging system'
  },
  {
    file: './add_personality_test_columns.sql',
    description: 'Add personality test columns'
  }
];

console.log('\n📋 DATABASE SETUP INSTRUCTIONS');
console.log('================================');
console.log('Copy and paste each script below into your Supabase SQL Editor:\n');

scripts.forEach((script, index) => {
  console.log(`${index + 1}. ${script.description.toUpperCase()}`);
  console.log('-'.repeat(50));
  
  try {
    const content = fs.readFileSync(script.file, 'utf8');
    console.log(content);
    console.log('\n' + '='.repeat(80) + '\n');
  } catch (error) {
    console.error(`❌ Could not read ${script.file}: ${error.message}`);
  }
});

console.log('🎯 AFTER APPLYING ALL SCRIPTS:');
console.log('1. Start dev server: npm run dev');
console.log('2. Test discovery page tagging buttons');
console.log('3. Test personality test at /personality-test');
console.log('4. Verify no 403 Forbidden errors');
