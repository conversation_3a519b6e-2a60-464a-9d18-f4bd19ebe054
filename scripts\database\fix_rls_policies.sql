-- Fix RLS policies for user_interest_tags if needed
-- Run this AFTER the constraint fix if you still get RLS errors

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can create tags for others" ON user_interest_tags;
DROP POLICY IF EXISTS "Users can view tags they created or received" ON user_interest_tags;

-- Create simple, working policies
-- Allow users to insert tags where they are the tagger
CREATE POLICY "Allow tag creation" ON user_interest_tags
    FOR INSERT 
    WITH CHECK (auth.uid()::text = tagger_id::text);

-- Allow users to read tags where they are either tagger or tagged user
CREATE POLICY "Allow tag reading" ON user_interest_tags
    FOR SELECT 
    USING (
        auth.uid()::text = tagger_id::text OR 
        auth.uid()::text = tagged_user_id::text
    );

-- Allow users to update their own tags
CREATE POLICY "Allow tag updates" ON user_interest_tags
    FOR UPDATE 
    USING (auth.uid()::text = tagger_id::text);

-- Allow users to delete their own tags
CREATE POLICY "Allow tag deletion" ON user_interest_tags
    FOR DELETE 
    USING (auth.uid()::text = tagger_id::text);
