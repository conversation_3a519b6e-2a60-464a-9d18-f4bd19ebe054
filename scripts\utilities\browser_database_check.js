// Database Inspection Script for SoulSynk
// Run this in browser console to check current database state

// Copy-paste this entire script into browser console when logged into the app

(async function checkDatabaseState() {
  console.log('🔍 SoulSynk Database State Inspection');
  console.log('=' .repeat(50));
  
  // Get Supabase client from window (if available)
  const { supabase } = window;
  
  if (!supabase) {
    console.error('❌ Supabase client not found on window object');
    console.log('💡 Try running this script from the browser console while on the SoulSynk app');
    return;
  }
  
  try {
    // Check current session
    console.log('\n🔐 Checking current session...');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
      return;
    }
    
    if (session) {
      console.log('✅ Session found:');
      console.log(`   User ID: ${session.user.id}`);
      console.log(`   Email: ${session.user.email}`);
      console.log(`   Provider: ${session.user.app_metadata?.provider}`);
    } else {
      console.log('❌ No active session - please login first');
      return;
    }
    
    // Check all profiles in database
    console.log('\n📊 Checking all profiles...');
    const { data: allProfiles, error: profilesError, count } = await supabase
      .from('profiles')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });
      
    if (profilesError) {
      console.error('❌ Error fetching profiles:', profilesError);
      return;
    }
    
    console.log(`✅ Total profiles: ${count}`);
    
    if (allProfiles && allProfiles.length > 0) {
      console.log('\n👥 All registered profiles:');
      allProfiles.forEach((profile, index) => {
        const isCurrentUser = profile.id === session.user.id;
        console.log(`\n${index + 1}. ${isCurrentUser ? '👤 (YOU) ' : ''}Profile ID: ${profile.id}`);
        console.log(`   Username: ${profile.username || 'N/A'}`);
        console.log(`   Full Name: ${profile.full_name || 'N/A'}`);
        console.log(`   Age: ${profile.age || 'N/A'}`);
        console.log(`   Gender: ${profile.gender || 'N/A'}`);
        console.log(`   Created: ${new Date(profile.created_at).toLocaleString()}`);
        console.log(`   Avatar URL: ${profile.avatar_url ? 'Yes' : 'No'}`);
      });
    } else {
      console.log('❌ No profiles found in database!');
      console.log('💡 This explains why no users appear in discovery');
      return;
    }
    
    // Test browseProfiles function simulation
    console.log('\n🔍 Testing user discovery simulation...');
    const currentUserId = session.user.id;
    
    const { data: otherProfiles, error: browseError } = await supabase
      .from('profiles')
      .select('id, username, full_name, age, gender, avatar_url')
      .neq('id', currentUserId);
      
    if (browseError) {
      console.error('❌ Browse error:', browseError);
    } else {
      console.log(`✅ Discovery test results:`);
      console.log(`   Current user: ${currentUserId}`);
      console.log(`   Other users discoverable: ${otherProfiles?.length || 0}`);
      
      if (otherProfiles && otherProfiles.length > 0) {
        otherProfiles.forEach((profile, index) => {
          console.log(`   ${index + 1}. ${profile.username} (${profile.full_name})`);
        });
      } else {
        console.log('   ❌ No other users found for discovery');
      }
    }
    
    // Check for authentication issues
    console.log('\n🔐 Authentication diagnostics...');
    
    // Check if current user has a profile
    const { data: currentProfile, error: currentProfileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', currentUserId)
      .single();
      
    if (currentProfileError) {
      console.error('❌ Current user profile error:', currentProfileError);
      console.log('💡 This user may not have completed registration properly');
    } else {
      console.log('✅ Current user profile found:');
      console.log(`   Username: ${currentProfile.username}`);
      console.log(`   Full Name: ${currentProfile.full_name}`);
      console.log(`   Registration complete: ${currentProfile.username ? 'Yes' : 'No'}`);
    }
    
    // Final diagnosis
    console.log('\n🎯 DIAGNOSIS:');
    
    if (count === 0) {
      console.log('❌ NO PROFILES: No users have completed registration');
      console.log('💡 SOLUTION: Check authentication flow and profile creation');
    } else if (count === 1) {
      console.log('❌ SINGLE USER: Only one user has registered');
      console.log('💡 SOLUTION: Register second Roberto account');
    } else if (otherProfiles?.length === 0) {
      console.log('❌ QUERY ISSUE: Multiple users exist but discovery query fails');
      console.log('💡 SOLUTION: Debug browseProfiles function');
    } else {
      console.log('✅ EVERYTHING LOOKS GOOD: Multiple users exist and are discoverable');
      console.log('💡 Check frontend display logic');
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
})();

console.log('💡 To run this script:');
console.log('1. Open SoulSynk app in browser');
console.log('2. Login with a Google account');
console.log('3. Open browser console (F12)');
console.log('4. Copy and paste this entire script');
console.log('5. Check the output for database state');
