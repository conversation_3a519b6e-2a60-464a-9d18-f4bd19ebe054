-- Manual setup for User Tagging System
-- Copy and paste this into your Supabase SQL editor

-- Create user_interest_tags table
CREATE TABLE IF NOT EXISTS user_interest_tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tagger_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  tagged_user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  tag_type text NOT NULL CHECK (tag_type IN ('interested', 'very_interested', 'potential_match')),
  emoji text,
  created_at timestamptz DEFAULT now(),
  UNIQUE(tagger_id, tagged_user_id)
);

-- Create contact_willingness_status table
CREATE TABLE IF NOT EXISTS contact_willingness_status (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  is_open_to_contact boolean DEFAULT false,
  status_message text,
  updated_at timestamptz DEFAULT now()
);

-- Create user_notifications table
CREATE TABLE IF NOT EXISTS user_notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  type text NOT NULL CHECK (type IN ('interest_tag', 'contact_willingness', 'mutual_interest')),
  title text NOT NULL,
  message text NOT NULL,
  data jsonb,
  read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE user_interest_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_willingness_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_interest_tags
CREATE POLICY "Users can view tags they created or received" ON user_interest_tags
  FOR SELECT USING (
    auth.uid() = tagger_id OR 
    auth.uid() = tagged_user_id
  );

CREATE POLICY "Users can create tags for others" ON user_interest_tags
  FOR INSERT WITH CHECK (auth.uid() = tagger_id);

CREATE POLICY "Users can update their own tags" ON user_interest_tags
  FOR UPDATE USING (auth.uid() = tagger_id);

CREATE POLICY "Users can delete their own tags" ON user_interest_tags
  FOR DELETE USING (auth.uid() = tagger_id);

-- RLS Policies for contact_willingness_status
CREATE POLICY "Users can view all contact willingness status" ON contact_willingness_status
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own contact willingness" ON contact_willingness_status
  FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for user_notifications
CREATE POLICY "Users can view their own notifications" ON user_notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON user_notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS user_interest_tags_tagger_id_idx ON user_interest_tags(tagger_id);
CREATE INDEX IF NOT EXISTS user_interest_tags_tagged_user_id_idx ON user_interest_tags(tagged_user_id);
CREATE INDEX IF NOT EXISTS contact_willingness_status_user_id_idx ON contact_willingness_status(user_id);
CREATE INDEX IF NOT EXISTS user_notifications_user_id_idx ON user_notifications(user_id);
CREATE INDEX IF NOT EXISTS user_notifications_read_idx ON user_notifications(read);

-- Function to trigger notifications when contact willingness changes
CREATE OR REPLACE FUNCTION notify_interested_users()
RETURNS TRIGGER AS $$
BEGIN
  -- Only notify if user becomes open to contact
  IF NEW.is_open_to_contact = true AND (OLD.is_open_to_contact IS NULL OR OLD.is_open_to_contact = false) THEN
    -- Insert notifications for users who have tagged this user
    INSERT INTO user_notifications (user_id, type, title, message, data)
    SELECT 
      uit.tagger_id,
      'contact_willingness',
      'Someone is now open to contact!',
      (SELECT full_name FROM profiles WHERE id = NEW.user_id) || ' is now open to being contacted.',
      jsonb_build_object('tagged_user_id', NEW.user_id, 'tag_type', uit.tag_type)
    FROM user_interest_tags uit
    WHERE uit.tagged_user_id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for contact willingness notifications
CREATE TRIGGER contact_willingness_notification_trigger
  AFTER INSERT OR UPDATE ON contact_willingness_status
  FOR EACH ROW
  EXECUTE FUNCTION notify_interested_users();
