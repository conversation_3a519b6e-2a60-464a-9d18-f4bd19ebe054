import React, { useState, useEffect } from 'react';
import { X, Shield, Clock, MessageSquare, UserX, AlertTriangle, Plus, Trash2 } from 'lucide-react';
import { ConversationBoundaries } from '../../types';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { 
  getConversationBoundaries, 
  updateConversationBoundaries 
} from '../../lib/conversationBoundaries';
import { LoadingSpinner } from '../ui/LoadingSpinner';

interface ConversationBoundariesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ConversationBoundariesModal: React.FC<ConversationBoundariesModalProps> = ({
  isOpen,
  onClose
}) => {
  const { user } = useAuth();
  const { showSuccess, showError, showLoading, dismiss } = useToast();
  const [boundaries, setBoundaries] = useState<ConversationBoundaries | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [newCustomBoundary, setNewCustomBoundary] = useState('');

  useEffect(() => {
    if (isOpen && user?.id) {
      loadBoundaries();
    }
  }, [isOpen, user?.id]);

  const loadBoundaries = async () => {
    if (!user?.id) return;
    
    setLoading(true);
    try {
      const data = await getConversationBoundaries(user.id);
      setBoundaries(data);
    } catch (err) {
      showError('Failed to load conversation boundaries');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!user?.id || !boundaries) return;

    setSaving(true);
    const loadingToastId = showLoading('Saving boundaries...');

    try {
      const result = await updateConversationBoundaries(user.id, boundaries);
      
      if (result.success) {
        showSuccess('Conversation boundaries updated successfully');
        onClose();
      } else {
        showError(result.error || 'Failed to update boundaries');
      }
    } catch (err) {
      showError('Failed to save boundaries');
    } finally {
      setSaving(false);
      dismiss(loadingToastId);
    }
  };

  const updateBoundary = (field: keyof ConversationBoundaries, value: any) => {
    if (!boundaries) return;
    setBoundaries({ ...boundaries, [field]: value });
  };

  const addCustomBoundary = () => {
    if (!boundaries || !newCustomBoundary.trim()) return;
    
    const updatedBoundaries = [...boundaries.custom_boundaries, newCustomBoundary.trim()];
    setBoundaries({ ...boundaries, custom_boundaries: updatedBoundaries });
    setNewCustomBoundary('');
  };

  const removeCustomBoundary = (index: number) => {
    if (!boundaries) return;
    
    const updatedBoundaries = boundaries.custom_boundaries.filter((_, i) => i !== index);
    setBoundaries({ ...boundaries, custom_boundaries: updatedBoundaries });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Shield className="w-6 h-6 text-purple-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-800">Conversation Boundaries</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="lg" />
            </div>
          ) : boundaries ? (
            <div className="space-y-8">
              {/* Message Permissions */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <MessageSquare className="w-5 h-5 text-purple-500 mr-2" />
                  Who Can Message You
                </h3>
                <div className="space-y-3">
                  {[
                    { value: 'everyone', label: 'Everyone', desc: 'Anyone can send you messages' },
                    { value: 'tagged_only', label: 'Tagged Users Only', desc: 'Only users who have tagged you with interest' },
                    { value: 'mutual_interest', label: 'Mutual Interest', desc: 'Only users with whom you have mutual interest tags' },
                    { value: 'none', label: 'No One', desc: 'Block all incoming messages' }
                  ].map((option) => (
                    <label key={option.value} className="flex items-start cursor-pointer">
                      <input
                        type="radio"
                        name="allow_messages_from"
                        value={option.value}
                        checked={boundaries.allow_messages_from === option.value}
                        onChange={(e) => updateBoundary('allow_messages_from', e.target.value)}
                        className="mt-1 text-purple-600 focus:ring-purple-500"
                      />
                      <div className="ml-3">
                        <div className="font-medium text-gray-800">{option.label}</div>
                        <div className="text-sm text-gray-600">{option.desc}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Response Time Expectations */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <Clock className="w-5 h-5 text-purple-500 mr-2" />
                  Response Time Expectations
                </h3>
                <select
                  value={boundaries.response_time_expectation}
                  onChange={(e) => updateBoundary('response_time_expectation', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="immediate">Immediate (within minutes)</option>
                  <option value="within_hours">Within a few hours</option>
                  <option value="within_day">Within a day</option>
                  <option value="flexible">Flexible (no expectations)</option>
                </select>
              </div>

              {/* Content Filtering */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <AlertTriangle className="w-5 h-5 text-purple-500 mr-2" />
                  Content Filtering
                </h3>
                <div className="space-y-3">
                  {[
                    { key: 'block_explicit_content', label: 'Block explicit content', desc: 'Automatically filter inappropriate messages' },
                    { key: 'block_personal_questions_early', label: 'Block personal questions early', desc: 'Prevent overly personal questions in early conversations' },
                    { key: 'block_meeting_requests_early', label: 'Block meeting requests early', desc: 'Prevent requests to meet in person too soon' },
                    { key: 'block_contact_sharing_early', label: 'Block contact sharing early', desc: 'Prevent sharing of phone numbers, social media, etc. too soon' },
                    { key: 'require_introduction', label: 'Require introduction', desc: 'Users must introduce themselves before starting a conversation' }
                  ].map((option) => (
                    <label key={option.key} className="flex items-start cursor-pointer">
                      <input
                        type="checkbox"
                        checked={boundaries[option.key as keyof ConversationBoundaries] as boolean}
                        onChange={(e) => updateBoundary(option.key as keyof ConversationBoundaries, e.target.checked)}
                        className="mt-1 text-purple-600 focus:ring-purple-500 rounded"
                      />
                      <div className="ml-3">
                        <div className="font-medium text-gray-800">{option.label}</div>
                        <div className="text-sm text-gray-600">{option.desc}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Custom Boundaries */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Custom Boundaries</h3>
                <div className="space-y-3">
                  {boundaries.custom_boundaries.map((boundary, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                      <span className="text-gray-800">{boundary}</span>
                      <button
                        onClick={() => removeCustomBoundary(index)}
                        className="text-red-500 hover:text-red-700 transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                  
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={newCustomBoundary}
                      onChange={(e) => setNewCustomBoundary(e.target.value)}
                      placeholder="Add a custom boundary..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      onKeyPress={(e) => e.key === 'Enter' && addCustomBoundary()}
                    />
                    <button
                      onClick={addCustomBoundary}
                      disabled={!newCustomBoundary.trim()}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Failed to load conversation boundaries
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={saving || !boundaries}
            className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
          >
            {saving ? (
              <>
                <LoadingSpinner size="sm" color="text-white" />
                <span className="ml-2">Saving...</span>
              </>
            ) : (
              'Save Changes'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConversationBoundariesModal;
