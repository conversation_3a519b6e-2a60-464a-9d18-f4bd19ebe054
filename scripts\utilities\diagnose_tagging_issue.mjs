import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVnZ2NjYmtoeHBlbXd1dHNxc3BoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc4MDQ5MDgsImV4cCI6MjA1MzM4MDkwOH0.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function diagnoseTaggingIssue() {
    console.log('🔍 Diagnosing tagging system issue...\n');

    try {
        // Check authentication
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        
        if (!user) {
            console.log('❌ Not authenticated - please log in to the app first');
            return;
        }
        
        console.log(`✅ Authenticated as: ${user.email}`);
        console.log(`User ID: ${user.id}`);

        // Check if user_interest_tags table exists and is accessible
        console.log('\n1️⃣ Testing table access...');
        const { data: existingTags, error: readError } = await supabase
            .from('user_interest_tags')
            .select('*')
            .limit(1);
            
        if (readError) {
            console.error('❌ Cannot read user_interest_tags table:', readError.message);
            if (readError.message.includes('relation "user_interest_tags" does not exist')) {
                console.log('💡 Solution: The user_interest_tags table needs to be created');
                console.log('👉 Apply the complete_fix.sql script to create missing tables');
            }
            return;
        } else {
            console.log(`✅ Table accessible - found ${existingTags?.length || 0} existing tags`);
        }

        // Check current user's profile
        console.log('\n2️⃣ Checking user profile...');
        const { data: currentProfile, error: profileError } = await supabase
            .from('profiles')
            .select('id, username, full_name')
            .eq('id', user.id)
            .single();
            
        if (profileError) {
            console.error('❌ Cannot access user profile:', profileError.message);
            return;
        } else {
            console.log('✅ User profile found:', currentProfile);
        }

        // Find other users to test tagging with
        console.log('\n3️⃣ Finding other users for tagging test...');
        const { data: otherUsers, error: usersError } = await supabase
            .from('profiles')
            .select('id, username, full_name')
            .neq('id', user.id)
            .limit(3);
            
        if (usersError) {
            console.error('❌ Cannot find other users:', usersError.message);
            return;
        }
        
        if (!otherUsers || otherUsers.length === 0) {
            console.log('⚠️ No other users found to test tagging with');
            console.log('💡 Solution: Add test users using the complete_fix.sql script');
            return;
        }
        
        console.log(`✅ Found ${otherUsers.length} other users for testing`);
        const testUser = otherUsers[0];
        console.log(`📝 Will test tagging user: ${testUser.username} (${testUser.id})`);

        // Test creating a tag (this should trigger the 403 error)
        console.log('\n4️⃣ Testing tag creation...');
        const { data: newTag, error: tagError } = await supabase
            .from('user_interest_tags')
            .insert({
                tagger_id: user.id,
                tagged_user_id: testUser.id,
                tag_type: 'interested',
                emoji: '👍'
            })
            .select();
            
        if (tagError) {
            console.error('❌ Tag creation failed:', tagError.message);
            console.log('\n🔧 DIAGNOSIS:');
            
            if (tagError.message.includes('row-level security')) {
                console.log('• Issue: RLS policies are blocking the insert');
                console.log('• Root cause: The RLS policies require specific conditions');
                console.log('• Solution: Update RLS policies to be more permissive');
                console.log('\n📋 Manual fix steps:');
                console.log('1. Go to Supabase Dashboard → SQL Editor');
                console.log('2. Run this SQL:');
                console.log(`
DROP POLICY IF EXISTS "Users can create interest tags" ON public.user_interest_tags;
CREATE POLICY "Allow authenticated users to insert tags" ON public.user_interest_tags
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND auth.uid()::text = tagger_id::text);
                `);
            } else if (tagError.message.includes('foreign key')) {
                console.log('• Issue: Foreign key constraint violation');
                console.log('• Root cause: Referenced user profile might not exist');
            } else {
                console.log(`• Issue: ${tagError.message}`);
            }
        } else {
            console.log('✅ Tag creation successful:', newTag);
            
            // Clean up test tag
            const { error: deleteError } = await supabase
                .from('user_interest_tags')
                .delete()
                .eq('id', newTag[0].id);
                
            if (!deleteError) {
                console.log('🧹 Test tag cleaned up');
            }
        }

    } catch (error) {
        console.error('❌ Unexpected error:', error);
    }
}

diagnoseTaggingIssue();
