import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { MessageCircle, Star } from 'lucide-react';
import { DigitalTwin } from '../../types';
import { useConversation } from '../../context/ConversationContext';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { useToast } from '../../hooks/useToast';

interface SuggestedMatchesProps {
  suggestedTwins: DigitalTwin[];
  isLoading?: boolean;
}

export const SuggestedMatches: React.FC<SuggestedMatchesProps> = ({ 
  suggestedTwins = [],
  isLoading = false
}) => {
  const { startConversation, setActiveConversation } = useConversation();
  const { showError } = useToast();

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (suggestedTwins.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No suggested matches yet. Check back soon!</p>
      </div>
    );
  }

  const handleStartConversation = async (twin: DigitalTwin) => {
    try {
      const conversation = await startConversation(twin, 'twin');
      if (conversation) {
        setActiveConversation(conversation.id);
      } else {
        showError('Failed to start conversation. Please make sure the twin exists in the database.');
      }
    } catch (error) {
      showError('Failed to start conversation');
      console.error('Error starting conversation:', error);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {suggestedTwins.slice(0, 4).map((twin) => (
        <div 
          key={twin.id} 
          className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition duration-300 group"
        >
          <div className="flex h-full">
            <div className="w-1/3 bg-gray-100 flex items-center justify-center">
              {twin.avatar ? (
                <img
                  src={twin.avatar}
                  alt={twin.name}
                  className="w-20 h-20 rounded-full object-cover shadow-inner"
                />
              ) : (
                <div className="w-20 h-20 rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white text-xl font-bold shadow-inner">
                  {twin.name?.charAt(0) || '?'}
                </div>
              )}
            </div>
            <div className="w-2/3 p-4">
              <div className="flex justify-between items-start">
                <h3 className="font-semibold text-gray-800">{twin.name}</h3>
                <div className="flex items-center text-yellow-500 text-sm">
                  <Star className="w-4 h-4 fill-current" />
                  <span className="ml-1">{Math.floor(Math.random() * 20 + 80)}%</span>
                </div>
              </div>
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                {/* Use description from the extended DigitalTwin interface */}
                {(twin as any).description || 'No description available'}
              </p>
              <div className="mt-2">
                <div className="flex flex-wrap gap-1">
                  {/* Handle both string[] and Interest[] types */}
                  {twin.interests?.slice(0, 2).map((interest, index) => (
                    <span 
                      key={index}
                      className="px-2 py-0.5 bg-purple-100 text-purple-800 text-xs rounded-full"
                    >
                      {typeof interest === 'string' ? interest : (interest as any).name}
                    </span>
                  ))}
                </div>
              </div>
              <div className="mt-3 group-hover:opacity-100 opacity-90 transition duration-300">
                <button
                  onClick={() => handleStartConversation(twin)}
                  className="w-full flex items-center justify-center px-3 py-1.5 bg-gradient-to-r from-purple-600 to-pink-500 text-white text-sm font-medium rounded hover:from-purple-700 hover:to-pink-600 transition duration-200"
                >
                  <MessageCircle className="w-4 h-4 mr-1" />
                  Start Conversation
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
      
      {suggestedTwins.length > 4 && (
        <div className="md:col-span-2 mt-4 text-center">
          <Link 
            to="/matches" 
            className="inline-block px-5 py-2 bg-white border border-purple-500 text-purple-600 font-medium rounded-lg hover:bg-purple-50 transition duration-200"
          >
            View All Matches
          </Link>
        </div>
      )}
    </div>
  );
};