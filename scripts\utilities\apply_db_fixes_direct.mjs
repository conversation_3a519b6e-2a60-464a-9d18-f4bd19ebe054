import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVnZ2NjYmtoeHBlbXd1dHNxc3BoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc4MDQ5MDgsImV4cCI6MjA1MzM4MDkwOH0.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyDatabaseFixes() {
    console.log('🔧 Applying database fixes step by step...\n');
    
    try {
        // 1. Add personality test columns
        console.log('📊 Adding personality test columns...');
        const { data: personalityResult, error: personalityError } = await supabase.rpc('execute_sql', {
            query: `
                -- Add personality test result columns
                ALTER TABLE public.profiles 
                ADD COLUMN IF NOT EXISTS extraversion_score INTEGER DEFAULT NULL,
                ADD COLUMN IF NOT EXISTS agreeableness_score INTEGER DEFAULT NULL,
                ADD COLUMN IF NOT EXISTS conscientiousness_score INTEGER DEFAULT NULL,
                ADD COLUMN IF NOT EXISTS neuroticism_score INTEGER DEFAULT NULL,
                ADD COLUMN IF NOT EXISTS openness_score INTEGER DEFAULT NULL,
                ADD COLUMN IF NOT EXISTS personality_test_completed_at TIMESTAMPTZ DEFAULT NULL,
                ADD COLUMN IF NOT EXISTS personality_insights JSONB DEFAULT NULL;
                
                SELECT 'Personality test columns added' as status;
            `
        });
        
        if (personalityError) {
            console.log('❌ Error adding personality columns:', personalityError);
        } else {
            console.log('✅ Personality test columns added successfully');
        }

        // 2. Create missing tables
        console.log('\n📋 Creating missing tables...');
        const { data: tablesResult, error: tablesError } = await supabase.rpc('execute_sql', {
            query: `
                -- Create user_interest_tags table
                CREATE TABLE IF NOT EXISTS public.user_interest_tags (
                  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
                  tagger_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
                  tagged_user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
                  tag_type text NOT NULL CHECK (tag_type IN ('interested', 'very_interested', 'potential_match')),
                  emoji text,
                  created_at timestamptz DEFAULT now(),
                  UNIQUE(tagger_id, tagged_user_id)
                );

                -- Create contact_willingness_status table
                CREATE TABLE IF NOT EXISTS public.contact_willingness_status (
                  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
                  user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
                  is_open_to_contact boolean DEFAULT false,
                  status_message text,
                  updated_at timestamptz DEFAULT now()
                );

                -- Create user_notifications table
                CREATE TABLE IF NOT EXISTS public.user_notifications (
                  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
                  user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
                  type text NOT NULL CHECK (type IN ('interest_tag', 'contact_willingness', 'mutual_interest')),
                  title text NOT NULL,
                  message text NOT NULL,
                  data jsonb,
                  read boolean DEFAULT false,
                  created_at timestamptz DEFAULT now()
                );
                
                SELECT 'Tables created' as status;
            `
        });
        
        if (tablesError) {
            console.log('❌ Error creating tables:', tablesError);
        } else {
            console.log('✅ Missing tables created successfully');
        }

        // 3. Enable RLS
        console.log('\n🔒 Enabling Row Level Security...');
        const { data: rlsResult, error: rlsError } = await supabase.rpc('execute_sql', {
            query: `
                -- Enable RLS
                ALTER TABLE public.user_interest_tags ENABLE ROW LEVEL SECURITY;
                ALTER TABLE public.contact_willingness_status ENABLE ROW LEVEL SECURITY;
                ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;
                
                SELECT 'RLS enabled' as status;
            `
        });
        
        if (rlsError) {
            console.log('❌ Error enabling RLS:', rlsError);
        } else {
            console.log('✅ Row Level Security enabled successfully');
        }

        // 4. Add RLS policies
        console.log('\n🛡️ Adding RLS policies...');
        const { data: policiesResult, error: policiesError } = await supabase.rpc('execute_sql', {
            query: `
                -- Drop existing policies if they exist (to avoid conflicts)
                DROP POLICY IF EXISTS "Users can view interest tags they created or received" ON public.user_interest_tags;
                DROP POLICY IF EXISTS "Users can create interest tags" ON public.user_interest_tags;
                DROP POLICY IF EXISTS "Users can view all contact willingness status" ON public.contact_willingness_status;
                DROP POLICY IF EXISTS "Users can manage their own contact willingness" ON public.contact_willingness_status;
                DROP POLICY IF EXISTS "Users can view their own notifications" ON public.user_notifications;
                DROP POLICY IF EXISTS "Users can update their own notifications" ON public.user_notifications;

                -- Add RLS policies
                CREATE POLICY "Users can view interest tags they created or received" ON public.user_interest_tags
                  FOR SELECT USING (auth.uid() = tagger_id OR auth.uid() = tagged_user_id);

                CREATE POLICY "Users can create interest tags" ON public.user_interest_tags
                  FOR INSERT WITH CHECK (auth.uid() = tagger_id);

                CREATE POLICY "Users can view all contact willingness status" ON public.contact_willingness_status
                  FOR SELECT USING (true);

                CREATE POLICY "Users can manage their own contact willingness" ON public.contact_willingness_status
                  FOR ALL USING (auth.uid() = user_id);

                CREATE POLICY "Users can view their own notifications" ON public.user_notifications
                  FOR SELECT USING (auth.uid() = user_id);

                CREATE POLICY "Users can update their own notifications" ON public.user_notifications
                  FOR UPDATE USING (auth.uid() = user_id);
                
                SELECT 'RLS policies added' as status;
            `
        });
        
        if (policiesError) {
            console.log('❌ Error adding RLS policies:', policiesError);
        } else {
            console.log('✅ RLS policies added successfully');
        }

        console.log('\n✅ All database fixes applied successfully!');
        
    } catch (error) {
        console.error('❌ Unexpected error:', error);
    }
}

// Check if execute_sql function exists, if not use direct query approach
async function checkAndApplyFixes() {
    try {
        // Try to check if the function exists
        const { data, error } = await supabase.rpc('execute_sql', {
            query: 'SELECT 1 as test;'
        });
        
        if (error && error.message.includes('function execute_sql')) {
            console.log('⚠️ execute_sql function not available, using direct SQL approach...');
            await applyDirectSQLFixes();
        } else {
            await applyDatabaseFixes();
        }
    } catch (err) {
        console.log('⚠️ RPC approach failed, using direct SQL approach...');
        await applyDirectSQLFixes();
    }
}

async function applyDirectSQLFixes() {
    console.log('🔧 Applying database fixes using direct SQL approach...\n');
    
    try {
        // Add personality test columns directly
        console.log('📊 Adding personality test columns...');
        const { error: personalityError } = await supabase
            .from('profiles')
            .select('id, extraversion_score')
            .limit(1);
        
        if (personalityError && personalityError.message.includes('column "extraversion_score" does not exist')) {
            console.log('❌ Personality test columns missing - manual database migration needed');
            console.log('Please run these SQL commands manually in your Supabase SQL editor:');
            console.log(`
-- Add personality test columns
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS extraversion_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS agreeableness_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS conscientiousness_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS neuroticism_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS openness_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS personality_test_completed_at TIMESTAMPTZ DEFAULT NULL,
ADD COLUMN IF NOT EXISTS personality_insights JSONB DEFAULT NULL;
            `);
        } else {
            console.log('✅ Personality test columns already exist or accessible');
        }

        // Check if missing tables exist
        console.log('\n📋 Checking for missing tables...');
        const { error: tagsError } = await supabase
            .from('user_interest_tags')
            .select('id')
            .limit(1);
        
        if (tagsError) {
            console.log('❌ Missing tables detected - manual database migration needed');
            console.log('Please run the complete_fix.sql script manually in your Supabase SQL editor');
        } else {
            console.log('✅ Required tables exist');
        }

        console.log('\n⚠️ Manual database migration may be required');
        console.log('Check your Supabase dashboard SQL editor to run the migration scripts');
        
    } catch (error) {
        console.error('❌ Error in direct SQL approach:', error);
    }
}

checkAndApplyFixes();
