import React, { useState } from 'react';
import { X } from 'lucide-react';

interface StartConversationModalProps {
  /** 
   * The digital twin object. 
   * <PERSON><PERSON>pe inferred from TwinProfile: 
   * - name: string 
   * - age?: number 
   * - gender?: string 
   * - bio?: string 
   * - personalityTraits?: { id: string | number; name: string; strength: number }[] 
   * - interests?: { id: string | number; name: string }[] 
   * - communicationStyle?: string[] 
   * - relationshipGoals?: string[] 
   * - compatibilityScore?: number 
   */
  digitalTwin: {
    name: string;
    age?: number;
    gender?: string;
    bio?: string;
    personalityTraits?: { id: string | number; name: string; strength: number }[];
    interests?: { id: string | number; name: string }[];
    communicationStyle?: string[];
    relationshipGoals?: string[];
    compatibilityScore?: number;
  };
  /** 
   * The real user object. 
   * <PERSON>hape inferred from UserProfile: 
   * - name: string 
   * - avatar?: string 
   * - bio?: string 
   * - interests?: string[] 
   */
  realUser: {
    name: string;
    avatar?: string;
    bio?: string;
    interests?: string[];
  };
  onConfirm: (chatType: 'twin' | 'real_user') => void;
  onCancel: () => void;
}

export const StartConversationModal: React.FC<StartConversationModalProps> = ({
  digitalTwin,
  realUser,
  onConfirm,
  onCancel
}) => {
  const [chatType, setChatType] = useState<'twin' | 'real_user'>('twin');

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 animate-fadeIn">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">Start New Conversation</h2>
          <button
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700"
            aria-label="Close"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Chat-type selector */}
        <div className="flex space-x-2 mb-6">
          <button
            className={`flex-grow px-4 py-2 rounded-lg transition ${
              chatType === 'twin'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
            onClick={() => setChatType('twin')}
          >
            Digital Twin
          </button>
          <button
            className={`flex-grow px-4 py-2 rounded-lg transition ${
              chatType === 'real_user'
                ? 'bg-purple-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
            onClick={() => setChatType('real_user')}
          >
            Real User
          </button>
        </div>

        {/* Info */}
        <div className="mb-6 text-sm text-gray-600">
          <p className="mb-2">
            You're about to start a conversation with{' '}
            {chatType === 'twin' ? digitalTwin.name : realUser.name}.
          </p>
          <ul className="list-disc list-inside space-y-1">
            <li>Automatically deleted after 1 week of inactivity</li>
            <li>Used to improve the AI's understanding of communication patterns</li>
            <li>Protected by end-to-end encryption</li>
          </ul>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={() => onConfirm(chatType)}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition duration-200"
          >
            Start Conversation
          </button>
        </div>
      </div>
    </div>
  );
};
