-- Fix database issues for tagging system
-- This script addresses:
-- 1. Check constraint violation for tag_type
-- 2. Row-level security policy violations
-- 3. Table structure inconsistencies

-- First, apply the missing constraint update for willing_to_contact tag type
ALTER TABLE user_interest_tags DROP CONSTRAINT IF EXISTS user_interest_tags_tag_type_check;
ALTER TABLE user_interest_tags ADD CONSTRAINT user_interest_tags_tag_type_check 
CHECK (tag_type IN ('interested', 'very_interested', 'potential_match', 'willing_to_contact'));

-- Update notification type constraints
ALTER TABLE user_notifications DROP CONSTRAINT IF EXISTS user_notifications_type_check;
ALTER TABLE user_notifications ADD CONSTRAINT user_notifications_type_check 
CHECK (type IN ('interest_tag', 'contact_willingness', 'mutual_interest', 'willing_to_contact'));

-- Ensure the user_interest_tags table has the correct structure
-- Note: The table should reference profiles(id) as per the original migration
ALTER TABLE user_interest_tags DROP CONSTRAINT IF EXISTS user_interest_tags_tagger_id_fkey;
ALTER TABLE user_interest_tags DROP CONSTRAINT IF EXISTS user_interest_tags_tagged_user_id_fkey;

-- Add back the foreign key constraints to profiles table
ALTER TABLE user_interest_tags ADD CONSTRAINT user_interest_tags_tagger_id_fkey 
FOREIGN KEY (tagger_id) REFERENCES profiles(id) ON DELETE CASCADE;
ALTER TABLE user_interest_tags ADD CONSTRAINT user_interest_tags_tagged_user_id_fkey 
FOREIGN KEY (tagged_user_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- Fix the unique constraint to allow multiple tag types per user pair
ALTER TABLE user_interest_tags DROP CONSTRAINT IF EXISTS user_interest_tags_tagger_id_tagged_user_id_key;
ALTER TABLE user_interest_tags ADD CONSTRAINT user_interest_tags_tagger_id_tagged_user_id_tag_type_key 
UNIQUE(tagger_id, tagged_user_id, tag_type);

-- Ensure contact_willingness_status table exists (this is the correct table name from migration)
-- The code references user_contact_willingness but the DB has contact_willingness_status

-- Fix foreign key constraints for user_notifications
ALTER TABLE user_notifications DROP CONSTRAINT IF EXISTS user_notifications_user_id_fkey;
ALTER TABLE user_notifications ADD CONSTRAINT user_notifications_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- Enable RLS on all tables
ALTER TABLE user_interest_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_contact_willingness ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_notifications ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can create interest tags" ON user_interest_tags;
DROP POLICY IF EXISTS "Users can view interest tags about them" ON user_interest_tags;
DROP POLICY IF EXISTS "Users can view their own interest tags" ON user_interest_tags;
DROP POLICY IF EXISTS "Users can update their own interest tags" ON user_interest_tags;
DROP POLICY IF EXISTS "Users can delete their own interest tags" ON user_interest_tags;

DROP POLICY IF EXISTS "Users can create contact willingness" ON user_contact_willingness;
DROP POLICY IF EXISTS "Users can view contact willingness about them" ON user_contact_willingness;
DROP POLICY IF EXISTS "Users can view their own contact willingness" ON user_contact_willingness;
DROP POLICY IF EXISTS "Users can update their own contact willingness" ON user_contact_willingness;
DROP POLICY IF EXISTS "Users can delete their own contact willingness" ON user_contact_willingness;

DROP POLICY IF EXISTS "Users can view their own notifications" ON user_notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON user_notifications;

-- Create RLS policies for user_interest_tags
CREATE POLICY "Users can create interest tags" ON user_interest_tags
    FOR INSERT WITH CHECK (auth.uid() = tagger_id);

CREATE POLICY "Users can view interest tags about them" ON user_interest_tags
    FOR SELECT USING (auth.uid() = tagged_user_id);

CREATE POLICY "Users can view their own interest tags" ON user_interest_tags
    FOR SELECT USING (auth.uid() = tagger_id);

CREATE POLICY "Users can update their own interest tags" ON user_interest_tags
    FOR UPDATE USING (auth.uid() = tagger_id);

CREATE POLICY "Users can delete their own interest tags" ON user_interest_tags
    FOR DELETE USING (auth.uid() = tagger_id);

-- Create RLS policies for user_contact_willingness
CREATE POLICY "Users can create contact willingness" ON user_contact_willingness
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view contact willingness about them" ON user_contact_willingness
    FOR SELECT USING (auth.uid() = target_user_id);

CREATE POLICY "Users can view their own contact willingness" ON user_contact_willingness
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own contact willingness" ON user_contact_willingness
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own contact willingness" ON user_contact_willingness
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for user_notifications
CREATE POLICY "Users can view their own notifications" ON user_notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON user_notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications" ON user_notifications
    FOR INSERT WITH CHECK (true);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_interest_tags_tagger ON user_interest_tags(tagger_id);
CREATE INDEX IF NOT EXISTS idx_user_interest_tags_tagged ON user_interest_tags(tagged_user_id);
CREATE INDEX IF NOT EXISTS idx_user_interest_tags_type ON user_interest_tags(tag_type);

CREATE INDEX IF NOT EXISTS idx_user_contact_willingness_user ON user_contact_willingness(user_id);
CREATE INDEX IF NOT EXISTS idx_user_contact_willingness_target ON user_contact_willingness(target_user_id);

CREATE INDEX IF NOT EXISTS idx_user_notifications_user ON user_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_read ON user_notifications(read);
CREATE INDEX IF NOT EXISTS idx_user_notifications_created ON user_notifications(created_at);

-- Add triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_user_interest_tags_updated_at ON user_interest_tags;
CREATE TRIGGER update_user_interest_tags_updated_at
    BEFORE UPDATE ON user_interest_tags
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_contact_willingness_updated_at ON user_contact_willingness;
CREATE TRIGGER update_user_contact_willingness_updated_at
    BEFORE UPDATE ON user_contact_willingness
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
