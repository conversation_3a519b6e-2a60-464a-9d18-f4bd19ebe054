// Database verification script for SoulSynk user discovery
// This script will check the current state of users in the database

import { supabase } from '../src/lib/supabase.ts';

console.log('🔍 SoulSynk Database User Discovery Debug');
console.log('=' .repeat(50));

async function checkDatabaseUsers() {
  try {
    console.log('📊 Checking all profiles in database...');
    
    // Get all profiles
    const { data: allProfiles, error: allProfilesError, count } = await supabase
      .from('profiles')
      .select('*', { count: 'exact' });
      
    if (allProfilesError) {
      console.error('❌ Error fetching profiles:', allProfilesError);
      return;
    }
    
    console.log(`\n✅ Total profiles found: ${count}`);
    
    if (allProfiles && allProfiles.length > 0) {
      console.log('\n👥 All registered users:');
      allProfiles.forEach((profile, index) => {
        console.log(`${index + 1}. ID: ${profile.id}`);
        console.log(`   Username: ${profile.username}`);
        console.log(`   Full Name: ${profile.full_name}`);
        console.log(`   Email: ${profile.email || 'N/A'}`);
        console.log(`   Created: ${profile.created_at}`);
        console.log(`   Age: ${profile.age || 'N/A'}`);
        console.log(`   Gender: ${profile.gender || 'N/A'}`);
        console.log('');
      });
    } else {
      console.log('❌ No profiles found in database!');
      console.log('💡 This might be why users cannot discover each other.');
    }
    
    // Test browseProfiles function for each user
    if (allProfiles && allProfiles.length > 0) {
      console.log('\n🔍 Testing browseProfiles for each user...');
      
      for (const profile of allProfiles) {
        console.log(`\n--- Testing discovery for ${profile.username} (${profile.id}) ---`);
        
        const { data: otherProfiles, error } = await supabase
          .from('profiles')
          .select('id, username, full_name, age, gender')
          .neq('id', profile.id);
          
        if (error) {
          console.error(`❌ Error for ${profile.username}:`, error);
        } else {
          console.log(`✅ ${profile.username} can discover ${otherProfiles?.length || 0} other users:`);
          otherProfiles?.forEach(other => {
            console.log(`   - ${other.username} (${other.full_name})`);
          });
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Check authentication
async function checkAuth() {
  const { data: { session }, error } = await supabase.auth.getSession();
  
  if (error) {
    console.log('❌ Auth error:', error);
    return null;
  }
  
  if (session) {
    console.log('✅ Current session found:');
    console.log(`   User ID: ${session.user.id}`);
    console.log(`   Email: ${session.user.email}`);
    console.log(`   Provider: ${session.user.app_metadata.provider}`);
  } else {
    console.log('❌ No active session found');
  }
  
  return session;
}

async function main() {
  console.log('🔐 Checking authentication...');
  const session = await checkAuth();
  
  console.log('\n🗄️ Checking database...');
  await checkDatabaseUsers();
  
  console.log('\n📋 Summary:');
  console.log('1. Check if users are properly registered after Google OAuth');
  console.log('2. Verify that profiles are created in the database');
  console.log('3. Test that browseProfiles excludes current user correctly');
  console.log('4. Ensure no filters are blocking discovery');
}

main().catch(console.error);
