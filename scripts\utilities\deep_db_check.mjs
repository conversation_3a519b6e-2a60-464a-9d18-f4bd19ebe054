// Enhanced database check to understand the schema and connection
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function deepDatabaseCheck() {
  console.log('🔍 Enhanced Database Analysis...');
  
  try {
    // Test basic connection
    console.log('1️⃣ Testing Supabase connection...');
    const { data: healthCheck, error: healthError } = await supabase
      .from('profiles')
      .select('count', { count: 'exact', head: true });
    
    if (healthError) {
      console.log('❌ Connection error:', healthError);
      return;
    }
    
    console.log('✅ Connection successful');
    console.log('📊 Total profiles count:', healthCheck);
    
    // Try to get profiles with minimal selection
    console.log('\n2️⃣ Fetching profiles with basic fields...');
    const { data: basicProfiles, error: basicError } = await supabase
      .from('profiles')
      .select('id, username, full_name')
      .limit(10);
    
    if (basicError) {
      console.log('❌ Basic profile fetch error:', basicError);
    } else {
      console.log('✅ Basic profiles found:', basicProfiles?.length || 0);
      if (basicProfiles && basicProfiles.length > 0) {
        basicProfiles.forEach((profile, index) => {
          console.log(`   Profile #${index + 1}:`, {
            id: profile.id,
            username: profile.username,
            name: profile.full_name
          });
        });
      }
    }
    
    // Check table structure
    console.log('\n3️⃣ Testing different field combinations...');
    
    // Test with all possible fields
    const fieldsToTest = [
      'id, username, full_name, created_at',
      'id, username, full_name, created_at, updated_at',
      'id, username, full_name, created_at, updated_at, age',
      '*'
    ];
    
    for (const fields of fieldsToTest) {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select(fields)
          .limit(1);
        
        if (error) {
          console.log(`❌ Fields "${fields}" - Error:`, error.message);
        } else {
          console.log(`✅ Fields "${fields}" - Found ${data?.length || 0} profiles`);
          if (data && data.length > 0) {
            console.log('   Sample data:', Object.keys(data[0]));
          }
        }
      } catch (err) {
        console.log(`❌ Fields "${fields}" - Exception:`, err.message);
      }
    }
    
    // Test with specific user ID from debug panel
    console.log('\n4️⃣ Testing with your specific user ID...');
    const yourUserId = '1818c8d3-c9a1-4a53-9b01-fc51e45ce55d';
    
    const { data: yourProfile, error: yourError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', yourUserId)
      .single();
    
    if (yourError) {
      console.log('❌ Your profile not found:', yourError);
    } else {
      console.log('✅ Your profile found:', yourProfile);
    }
    
  } catch (error) {
    console.error('❌ Deep check failed:', error);
  }
}

deepDatabaseCheck();
