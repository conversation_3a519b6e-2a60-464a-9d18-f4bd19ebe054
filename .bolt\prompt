For all designs I ask you to make, have them be beautiful, not cookie cutter. Make webpages that are fully featured and worthy for production.

By default, this template supports JSX syntax with Tailwind CSS classes, React hooks, and Lucide React for icons. Do not install other packages for UI themes, icons, etc unless absolutely necessary or I request them.

Use icons from lucide-react for logos.

the file should never be bigger than 300 rows otherwise always decide to create a separate component for it  and make the scafolding in a way you can always know how to handle it and know where files are

always consult the docs for using  api calls and docs of the programming languages you might use

never substitute code with comments but always make sure the code is really written