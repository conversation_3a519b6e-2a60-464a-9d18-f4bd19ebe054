# SoulTwinSync - AI-Powered Personality Matching Platform

A sophisticated React + TypeScript application that creates digital twins for users based on personality assessments and enables meaningful connections through AI-powered matching.

## 🚀 Quick Start

### 1. Database Setup
```bash
# Apply the main database fix in Supabase SQL Editor
scripts/database/FINAL_WORKING_FIX.sql
```

### 2. Environment Configuration
```env
VITE_SUPABASE_URL=<your project url>
VITE_SUPABASE_ANON_KEY=<your anon key>
VITE_AVATARS_BUCKET=avatars  # Optional if your bucket is not named "avatars"
```

### 3. Start Development
```bash
# Use the provided batch script
start-dev.bat

# Or manually
npm install
npm run dev
```

## 📁 Project Structure

- **`/src/`** - React TypeScript application source code
- **`/scripts/`** - Database and utility scripts
  - `/database/` - SQL setup and migration files
  - `/tests/` - Testing and verification scripts  
  - `/utilities/` - Maintenance and diagnostic tools
- **`/docs/`** - Project documentation
  - `/database/` - Database setup guides
  - `/setup/` - Configuration documentation
- **`/supabase/`** - Supabase configuration and migrations

## ✨ Key Features

- **🧠 Personality Assessment** - Big Five personality test (mandatory for digital twin creation)
- **🤖 Digital Twin System** - AI-powered personality modeling for better matches
- **🔍 Smart Discovery** - Personality-based user discovery with filtering
- **💬 Conversation System** - Real-time messaging between matched users  
- **🏷️ Interest Tagging** - "Very Interested" and "Willing to Contact" features
- **🔐 Authentication** - Secure Supabase auth with profile management
- **📱 Responsive Design** - Modern UI with Tailwind CSS

## 🎯 Core Functionality

1. **User Registration & Profile Creation**
2. **Mandatory Personality Test** - Required for digital twin functionality
3. **Personality-Based Discovery** - Find compatible matches
4. **Interest Expression** - Tag users you're interested in
5. **Real-time Conversations** - Chat with matched users

## 🛠️ Development

### Prerequisites
- Node.js 16+
- Supabase account and project
- Environment variables configured

### Setup Storage
Create a public storage bucket for user avatars in Supabase and ensure it allows uploads with your anon key. Avatar images will be stored in this bucket and referenced from the `profiles.avatar_url` column.

### Database Requirements
- Run `scripts/database/FINAL_WORKING_FIX.sql` for complete setup
- Creates all required tables and RLS policies
- Fixes 403 Forbidden errors
- Adds personality test fields

### Testing
```bash
# Test database functionality
node scripts/tests/test_database_fixes.mjs

# Verify discovery system
node scripts/tests/test_user_discovery.js

# Check tagging system
node scripts/tests/test_tagging_system.js
```

## 📚 Documentation

- **Project Structure**: `/docs/setup/PROJECT_STRUCTURE.md`
- **Database Setup**: `/docs/database/DATABASE_FIX_INSTRUCTIONS.md`
- **Tagging System**: `/docs/database/TAGGING_SYSTEM_SETUP.md`
- **Scripts Guide**: `/scripts/README.md`

## 🔧 Troubleshooting

### Common Issues:
1. **403 Forbidden Errors** - Run `FINAL_WORKING_FIX.sql`
2. **Personality Test Not Loading** - Check database personality columns
3. **Discovery Not Working** - Verify RLS policies are applied
4. **Tagging Fails** - Check `user_interest_tags` table exists

### Verification Tools:
```bash
# Check database state
node scripts/utilities/deep_db_check.mjs

# Verify RLS policies  
node scripts/utilities/test_rls_state.mjs

# Diagnose issues
node scripts/utilities/verification_checklist.js
```

## 🎨 Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **Build Tool**: Vite
- **Testing**: Jest
- **Routing**: React Router v6
- **State Management**: Context API + Zustand

---

**Ready to create meaningful connections through personality-based matching!** ✨
