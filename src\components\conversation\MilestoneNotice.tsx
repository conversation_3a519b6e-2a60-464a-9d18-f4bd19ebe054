import React from 'react';
import { Award, Calendar, Heart, Flag, Star } from 'lucide-react';
import { MilestoneType } from '../../types';

interface MilestoneNoticeProps {
  type: MilestoneType;
  description: string;
}

const MilestoneNotice: React.FC<MilestoneNoticeProps> = ({ type, description }) => {
  // Choose icon based on milestone type
  const renderIcon = () => {
    switch (type) {
      case MilestoneType.FIRST_MESSAGE:
        return <Award className="h-4 w-4 text-purple-500" />;
      case MilestoneType.SHARED_INTEREST:
        return <Star className="h-4 w-4 text-purple-500" />;
      case MilestoneType.DEEP_QUESTION:
        return <Heart className="h-4 w-4 text-purple-500" />;
      case MilestoneType.PERSONAL_STORY:
        return <Flag className="h-4 w-4 text-purple-500" />;
      case MilestoneType.CONTACT_INFO:
        return <Calendar className="h-4 w-4 text-purple-500" />;
      case MilestoneType.REAL_WORLD_PLAN:
        return <Calendar className="h-4 w-4 text-purple-500" />;
      default:
        return <Star className="h-4 w-4 text-purple-500" />;
    }
  };
  
  return (
    <div className="flex justify-center my-6">
      <div className="bg-purple-50 border border-purple-100 rounded-lg px-4 py-3 text-purple-800 text-sm flex items-center animate-fadeIn">
        {renderIcon()}
        <div className="mx-2">
          <span className="font-semibold">Milestone:</span> {description}
        </div>
      </div>
    </div>
  );
};

export default MilestoneNotice;