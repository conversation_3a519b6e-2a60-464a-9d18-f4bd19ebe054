import React, { useState, Fragment } from 'react';
import { Link } from 'react-router-dom';
import { Switch } from '@headlessui/react';
import { ChevronRight, Clock } from 'lucide-react';
import { Conversation } from '../../types';
import { formatDate } from '../../utils/dateUtils';
import { LoadingSpinner } from '../ui/LoadingSpinner';

interface RecentConversationsProps {
  conversations: Conversation[];
  isLoading?: boolean;
}

export const RecentConversations: React.FC<RecentConversationsProps> = ({
  conversations = [],
  isLoading = false,
}) => {
  /* Toggle state: show unread only */
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);

  /* Helper: detect if a conversation has unread messages */
const hasUnread = (c: Conversation) => {
  const last = c.messages?.[c.messages.length - 1];
  // consider unread if the last message was sent by the other party
  return last?.sender !== 'user';
};

const filteredConversations = showUnreadOnly
    ? conversations.filter(hasUnread)
    : conversations;

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No conversations yet. Start chatting with your matches!</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Toggle bar */}
      <div className="flex items-center justify-between">
        <span className="font-medium text-sm text-gray-700">Show unread only</span>
        <Switch
          checked={showUnreadOnly}
          onChange={setShowUnreadOnly}
          as={Fragment}
        >
          {({ checked }) => (
            <button
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors \
                ${
                  checked
                    ? 'bg-purple-600'
                    : 'bg-gray-300 dark:bg-gray-600/60'
                }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition \
                  ${checked ? 'translate-x-6' : 'translate-x-1'}`}
              />
            </button>
          )}
        </Switch>
      </div>

      {/* Conversation cards */}
      <div className="space-y-4">
        {filteredConversations.map((conversation) => {
          const lastMessage = conversation.messages?.[conversation.messages.length - 1];
          const participantName =
            conversation.type === 'twin'
              ? conversation.digitalTwin?.name
              : conversation.realUser?.name || 'User';
          const participantAvatar =
            conversation.type === 'twin'
              ? conversation.digitalTwin?.avatar
              : conversation.realUser?.avatar_url;

          return (
            <Link
              key={conversation.id}
              to={`/conversations/${conversation.id}`}
              className="block border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition duration-200"
            >
              <div className="flex items-start">
              <div className="flex-shrink-0 mr-4">
                {participantAvatar ? (
                  <img
                    src={participantAvatar}
                    alt={participantName}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white font-bold shadow-inner">
                    {participantName?.charAt(0) || '?'}
                  </div>
                )}
              </div>
                <div className="flex-grow min-w-0">
                  <div className="flex justify-between items-start">
                    <h3 className="font-semibold text-gray-800 truncate">{participantName}</h3>
                    <div className="flex items-center text-gray-500 text-xs">
                      <Clock className="w-3 h-3 mr-1" />
                      <span>{formatDate(conversation.lastActive)}</span>
                    </div>
                  </div>
                  {lastMessage ? (
                    <p className="text-sm text-gray-600 mt-1 truncate">
                      <span className="font-medium mr-1">
                        {lastMessage.sender === 'user' ? 'You:' : `${participantName}:`}
                      </span>
                      {lastMessage.content}
                    </p>
                  ) : (
                    <p className="text-sm text-gray-500 mt-1 italic">Start a conversation</p>
                  )}
                  <div className="mt-2 flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      {conversation.messages?.length || 0}{' '}
                      {conversation.messages?.length === 1 ? 'message' : 'messages'}
                    </div>
                    <ChevronRight className="w-4 h-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Link to view all */}
      {conversations.length > 0 && (
        <div className="text-center pt-4">
          <Link
            to="/conversations"
            className="inline-block px-5 py-2 bg-white border border-purple-500 text-purple-600 font-medium rounded-lg hover:bg-purple-50 transition duration-200"
          >
            View All Conversations
          </Link>
        </div>
      )}
    </div>
  );
};
