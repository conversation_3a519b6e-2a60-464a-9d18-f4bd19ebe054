import { supabase } from './supabase';
import { <PERSON><PERSON>rLogger } from './errorHandler';

export async function exportUserData(userId: string) {
  try {
    // Fetch user profile
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    // Fetch user's digital twins
    const { data: twins } = await supabase
      .from('digital_twins')
      .select('*')
      .eq('owner_id', userId);

    // Fetch chat history
    const { data: chatHistory } = await supabase
      .from('twin_chat_history')
      .select('*')
      .eq('user_id', userId);

    // Fetch training data
    const { data: trainingData } = await supabase
      .from('twin_training_data')
      .select('*')
      .eq('user_id', userId);

    return {
      profile,
      twins,
      chatHistory,
      trainingData,
      exportDate: new Date().toISOString()
    };
  } catch (error) {
    ErrorLogger.logError(error, 'database');
    throw new Error('Failed to export user data');
  }
}

export async function deleteUserData(userId: string) {
  try {
    // Delete in specific order to maintain referential integrity
    await supabase.from('twin_chat_history').delete().eq('user_id', userId);
    await supabase.from('twin_training_data').delete().eq('user_id', userId);
    await supabase.from('digital_twins').delete().eq('owner_id', userId);
    await supabase.from('profiles').delete().eq('id', userId);
    
    // Delete auth user
    const { error: authError } = await supabase.auth.admin.deleteUser(userId);
    if (authError) throw authError;

    return true;
  } catch (error) {
    ErrorLogger.logError(error, 'database');
    throw new Error('Failed to delete user data');
  }
}

export async function processWhatsAppChat(userId: string, chatContent: string) {
  try {
    // Store raw chat data
    const { data: trainingData, error: trainingError } = await supabase
      .from('twin_training_data')
      .insert({
        user_id: userId,
        data_source: 'whatsapp',
        content: chatContent,
        processed_status: 'pending'
      })
      .select()
      .single();

    if (trainingError) throw trainingError;

    // Process chat in chunks
    const chunks = chatContent.split('\n\n');
    for (const chunk of chunks) {
      await supabase.from('twin_chat_history').insert({
        user_id: userId,
        message: chunk,
        is_twin_message: false
      });
    }

    // Update training status
    await supabase
      .from('twin_training_data')
      .update({ processed_status: 'completed' })
      .eq('id', trainingData.id);

    return true;
  } catch (error) {
    ErrorLogger.logError(error, 'database');
    throw new Error('Failed to process WhatsApp chat');
  }
}

export async function saveUserFact(userId: string, fact: any) {
  // Always store fact as string
  const factStr = typeof fact === 'string' ? fact : JSON.stringify(fact);
  // Insert user fact (allow multiple facts per user)
  const { data, error } = await supabase
    .from('user_facts')
    .insert([{ user_id: userId, fact: factStr }]);
  if (error) throw error;
  return data;
}

export async function getUserFacts(userId: string): Promise<string[]> {
  const { data, error } = await supabase
    .from('user_facts')
    .select('fact')
    .eq('user_id', userId);
  if (error) throw error;
  return data?.map((row: any) => row.fact) || [];
}

export async function saveUserSummary(userId: string, summary: string) {
  // Upsert user summary
  const { data, error } = await supabase
    .from('user_summary')
    .upsert([{ user_id: userId, summary }], { onConflict: 'user_id' });
  if (error) throw error;
  return data;
}

export async function getUserSummary(userId: string): Promise<string> {
  const { data, error } = await supabase
    .from('user_summary')
    .select('summary')
    .eq('user_id', userId)
    .single();
  if (error) return '';
  return data?.summary || '';
}

export async function rescanAndExtractUserFacts(userId: string) {
  // Fetch all conversations and messages for the user
  const { fetchUserConversations } = await import('./conversationStorage');
  const allConvs = await fetchUserConversations(userId);
  const allMessages = allConvs.flatMap(c => c.messages || []);
  const musicRegex = /(electronic music|sidechain|techno|house|music|musica elettronica|synth|massive attack|tycho|pop|rock|jazz|blues|classical|indie|ambient|trance|drum and bass|dubstep|trap|hip hop|rap|metal|punk|folk|country|reggae|reggaeton|latin|k-pop|j-pop|edm|dance|orchestra|opera|soundtrack|colonna sonora|artist: [^,.;!?\n]+)/gi;
  for (const msg of allMessages) {
    if (typeof msg.content === 'string') {
      const matches = msg.content.match(musicRegex);
      if (matches) {
        for (const match of matches) {
          await saveUserFact(userId, `music_preference: ${match}`);
        }
      }
    }
  }
}