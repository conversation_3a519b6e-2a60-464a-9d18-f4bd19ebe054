-- Fix RLS policies for user_interest_tags table
-- This script addresses RLS policy violations

-- First, check current policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'user_interest_tags';

-- Drop existing policies that might be too restrictive
DROP POLICY IF EXISTS "Users can view interest tags they created or received" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can create interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can update their own interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can delete their own interest tags" ON public.user_interest_tags;

-- Create more permissive policies for user_interest_tags
CREATE POLICY "Enable read access for authenticated users" ON public.user_interest_tags
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Enable insert for authenticated users" ON public.user_interest_tags
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND auth.uid() = tagger_id);

CREATE POLICY "Enable update for tag creators" ON public.user_interest_tags
  FOR UPDATE USING (auth.uid() = tagger_id);

CREATE POLICY "Enable delete for tag creators" ON public.user_interest_tags
  FOR DELETE USING (auth.uid() = tagger_id);

-- Also fix contact_willingness_status policies
DROP POLICY IF EXISTS "Users can view all contact willingness status" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Users can manage their own contact willingness" ON public.contact_willingness_status;

CREATE POLICY "Enable read access for authenticated users" ON public.contact_willingness_status
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Enable insert for authenticated users" ON public.contact_willingness_status
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND auth.uid() = user_id);

CREATE POLICY "Enable update for owners" ON public.contact_willingness_status
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Enable delete for owners" ON public.contact_willingness_status
  FOR DELETE USING (auth.uid() = user_id);

-- Fix user_notifications policies
DROP POLICY IF EXISTS "Users can view their own notifications" ON public.user_notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON public.user_notifications;

CREATE POLICY "Enable read access for notification owners" ON public.user_notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Enable insert for system and users" ON public.user_notifications
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Enable update for notification owners" ON public.user_notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Enable delete for notification owners" ON public.user_notifications
  FOR DELETE USING (auth.uid() = user_id);

-- Fix profiles table policies if they're too restrictive
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;

CREATE POLICY "Enable read access for authenticated users" ON public.profiles
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Enable insert for authenticated users" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND auth.uid() = id);

CREATE POLICY "Enable update for profile owners" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Enable delete for profile owners" ON public.profiles
  FOR DELETE USING (auth.uid() = id);

SELECT 'RLS policies updated successfully' as status;
