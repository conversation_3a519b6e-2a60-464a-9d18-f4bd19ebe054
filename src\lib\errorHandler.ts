import { AuthError } from '@supabase/supabase-js';

export interface ErrorLog {
  timestamp: string;
  type: 'auth' | 'database' | 'realtime' | 'system';
  status?: number;
  message: string;
  details?: any;
  url?: string;
}

export interface NormalizedError {
  message: string;
  code?: string;
  status?: number;
  name?: string;
}

const normalizeError = (error: Error | AuthError | unknown): NormalizedError => {
  if (error instanceof Error) {
    return {
      message: error.message,
      name: error.name,
      code: (error as AuthError)?.code,
      status: (error as AuthError)?.status
    };
  }
  
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as any;
    return {
      message: errorObj.message || 'Unknown error',
      code: errorObj.code,
      status: errorObj.status,
      name: errorObj.name
    };
  }
  
  return {
    message: String(error) || 'Unknown error'
  };
};

export class ErrorLogger {
  private static logs: ErrorLog[] = [];
  private static readonly MAX_LOGS = 100;

  static logError(error: Error | AuthError | unknown, type: ErrorLog['type'], url?: string) {
    const errorLog: ErrorLog = {
      timestamp: new Date().toISOString(),
      type,
      message: error instanceof Error ? error.message : 'Unknown error',
      url
    };

    if ((error as AuthError)?.status) {
      errorLog.status = (error as AuthError).status;
    }

    if (error instanceof Error) {
      errorLog.details = {
        name: error.name,
        stack: error.stack
      };
    }

    this.logs.unshift(errorLog);
    if (this.logs.length > this.MAX_LOGS) {
      this.logs.pop();
    }

    console.error(`[${type.toUpperCase()}]`, errorLog);
    return errorLog;
  }

  static getLogs(type?: ErrorLog['type']) {
    return type ? this.logs.filter(log => log.type === type) : this.logs;
  }

  static clearLogs() {
    this.logs = [];
  }
}

export const handleAuthError = (error: AuthError | Error | unknown): string => {
  const normalizedError = normalizeError(error);
  if (normalizedError.code) {
    return 'Your session has expired. Please sign in again.';
  }

  if (error instanceof Error) {
    if (error.message.includes('PKCE')) {
      return 'Authentication flow was interrupted. Please try again.';
    }
    if (error.message.includes('timeout')) {
      return 'The authentication request timed out. Please check your connection and try again.';
    }
  }

  if (error instanceof Error && error.message.includes('personality assessment')) {
    return 'Complete personality assessment required for full access. Please complete the test.';
  }
  
  const errorMessage = error instanceof Error ? error.message : normalizedError.message;
  return `Authentication error: ${errorMessage.replace('AuthError: ', '')}`;
};

export const handleDatabaseError = (error: Error | unknown): string => {
  if (error instanceof Error) {
    if (error.message.includes('connection refused')) {
      return 'Unable to connect to the database. Please check your connection.';
    }
    if (error.message.includes('permission denied')) {
      return 'You don\'t have permission to perform this action.';
    }
  }

  return 'A database error occurred. Please try again later.';
};

export const handleRealtimeError = (error: Error | unknown): string => {
  if (error instanceof Error) {
    if (error.message.includes('websocket')) {
      return 'Real-time connection failed. Please check your connection.';
    }
    if (error.message.includes('subscription')) {
      return 'Failed to subscribe to updates. Please refresh the page.';
    }
  }

  return 'A real-time connection error occurred. Please refresh the page.';
};