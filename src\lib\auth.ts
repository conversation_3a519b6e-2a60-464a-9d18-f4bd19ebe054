// src/lib/auth.ts
import { supabase } from './supabase';
import { ErrorLogger } from './errorHandler';
import type { Database } from './database.types';

export interface AuthResponse {
  success: boolean;
  error?: { message: string; code?: string };
  session?: any;
  isNewUser?: boolean;
}

// Sign in via Google OAuth
export const signInWithGoogle = async (): Promise<AuthResponse> => {
  try {
    const redirectUrl = import.meta.env.VITE_APP_URL
      ? `${import.meta.env.VITE_APP_URL}/auth/callback`
      : window.location.origin + '/auth/callback';

    if (!redirectUrl) {
      return {
        success: false,
        error: { message: 'Redirect URL is missing', code: 'missing_redirect_url' }
      };
    }

    // Use PKCE flow for better security and session handling
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: redirectUrl,
        queryParams: { access_type: 'offline' },
        skipBrowserRedirect: false
      }
    });
    
    console.log('OAuth sign-in initiated with redirect URL:', redirectUrl);

    if (error) throw error;
    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'auth');
    return { success: false, error: { message: 'Google sign-in failed', code: 'google_signin_error' } };
  }
};

// Handle OAuth callback and ensure user profile
export const handleAuthCallback = async (): Promise<AuthResponse> => {
  try {
    console.log('Processing auth callback...');
    
    // With PKCE flow, Supabase automatically handles the code exchange
    // We just need to get the current session
    const {
      data: { session },
      error: getSessionError
    } = await supabase.auth.getSession();

    if (getSessionError) {
      console.error('Get session error:', getSessionError);
      throw getSessionError;
    }
    
    if (!session) {
      console.error('No session found after callback processing');
      return { success: false, error: { message: 'No session found', code: 'no_session' } };
    }
    
    console.log('Session found:', session.user.id);

    // Use untyped filter to accept plain string ID
    const userId: string = session.user.id;

    // Check existing profile with .filter
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .filter('id', 'eq', userId)
      .single();

    if (profileError && profileError.code !== 'PGRST116') throw profileError;
    const isNewUser = !profile;

    // inside handleAuthCallback
if (isNewUser) {
  // ✅ this object exactly matches Database['public']['Tables']['profiles']['Insert']
const newProfile: Database['public']['Tables']['profiles']['Insert'] = {
  id: userId,                                                // satisfies RLS
  username: session.user.email?.split('@')[0] ?? `user_${Date.now()}`,
  full_name: session.user.user_metadata?.full_name ?? null,  // undefined ➜ null
  avatar_url: session.user.user_metadata?.avatar_url ?? null // undefined ➜ null
  // ⛔ DO NOT include created_at / updated_at
};

// Either style works; this calls overload 1 (single object)
const { error: insertError } = await supabase
  .from('profiles')
  .insert(newProfile);      // see docs: accepts object or array :contentReference[oaicite:5]{index=5}
if (insertError) throw insertError;

}


    return { success: true, session, isNewUser };
  } catch (err) {
    ErrorLogger.logError(err, 'auth');
    return { success: false, error: { message: 'Auth callback failed', code: 'auth_callback_error' } };
  }
};

// Get current session
export const getSession = async (): Promise<any> => {
  try {
    const {
      data: { session },
      error
    } = await supabase.auth.getSession();
    if (error) throw error;
    return session;
  } catch (err) {
    ErrorLogger.logError(err, 'auth');
    return null;
  }
};

// Refresh session token
export const refreshSession = async (): Promise<AuthResponse> => {
  try {
    const {
      data: { session },
      error
    } = await supabase.auth.refreshSession();
    if (error) throw error;
    return { success: true, session };
  } catch (err) {
    ErrorLogger.logError(err, 'auth');
    return { success: false, error: { message: 'Refresh failed', code: 'refresh_error' } };
  }
};

// Sign out
export const signOut = async (): Promise<AuthResponse> => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'auth');
    return { success: false, error: { message: 'Sign-out failed', code: 'signout_error' } };
  }
};
