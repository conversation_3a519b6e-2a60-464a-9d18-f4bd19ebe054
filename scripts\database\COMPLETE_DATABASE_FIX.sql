-- COMPREHENSIVE SOULTWINSYNC DATABASE FIX
-- Apply this entire script in your Supabase SQL Editor
-- This combines all the fixes needed for the application

-- ===================================================================
-- STEP 1: CREATE MISSING TABLES
-- ===================================================================

-- Create user_interest_tags table
CREATE TABLE IF NOT EXISTS public.user_interest_tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tagger_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  tagged_user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  tag_type text NOT NULL CHECK (tag_type IN ('interested', 'very_interested', 'potential_match')),
  emoji text,
  created_at timestamptz DEFAULT now(),
  UNIQUE(tagger_id, tagged_user_id)
);

-- Create contact_willingness_status table
CREATE TABLE IF NOT EXISTS public.contact_willingness_status (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  is_open_to_contact boolean DEFAULT false,
  status_message text,
  updated_at timestamptz DEFAULT now()
);

-- Create user_notifications table
CREATE TABLE IF NOT EXISTS public.user_notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  type text NOT NULL CHECK (type IN ('interest_tag', 'contact_willingness', 'mutual_interest')),
  title text NOT NULL,
  message text NOT NULL,
  data jsonb,
  read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE public.user_interest_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_willingness_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;

-- ===================================================================
-- STEP 2: FIX ALL RLS POLICIES
-- ===================================================================

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view interest tags they created or received" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can create interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can update their own interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Users can delete their own interest tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable update for tag creators" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Enable delete for tag creators" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Allow authenticated users to read all tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Allow authenticated users to insert tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Allow users to update their own tags" ON public.user_interest_tags;
DROP POLICY IF EXISTS "Allow users to delete their own tags" ON public.user_interest_tags;

-- Create new permissive policies for user_interest_tags
CREATE POLICY "Allow authenticated users to read all tags" ON public.user_interest_tags
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow authenticated users to insert tags" ON public.user_interest_tags
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL 
    AND auth.uid()::text = tagger_id::text
    AND EXISTS (SELECT 1 FROM public.profiles WHERE id = tagged_user_id)
  );

CREATE POLICY "Allow users to update their own tags" ON public.user_interest_tags
  FOR UPDATE USING (auth.uid()::text = tagger_id::text);

CREATE POLICY "Allow users to delete their own tags" ON public.user_interest_tags
  FOR DELETE USING (auth.uid()::text = tagger_id::text);

-- Fix profiles table policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for profile owners" ON public.profiles;
DROP POLICY IF EXISTS "Allow authenticated users to read profiles" ON public.profiles;
DROP POLICY IF EXISTS "Allow users to insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Allow users to update their own profile" ON public.profiles;

CREATE POLICY "Allow authenticated users to read profiles" ON public.profiles
  FOR SELECT USING (true); -- Allow reading all profiles for discovery

CREATE POLICY "Allow users to insert their own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid()::text = id::text);

CREATE POLICY "Allow users to update their own profile" ON public.profiles
  FOR UPDATE USING (auth.uid()::text = id::text);

-- Fix contact_willingness_status policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Enable update for owners" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Allow authenticated users to read contact status" ON public.contact_willingness_status;
DROP POLICY IF EXISTS "Allow users to manage their contact status" ON public.contact_willingness_status;

CREATE POLICY "Allow authenticated users to read contact status" ON public.contact_willingness_status
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow users to manage their contact status" ON public.contact_willingness_status
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Fix user_notifications policies
DROP POLICY IF EXISTS "Enable read access for notification owners" ON public.user_notifications;
DROP POLICY IF EXISTS "Enable insert for system and users" ON public.user_notifications;
DROP POLICY IF EXISTS "Enable update for notification owners" ON public.user_notifications;
DROP POLICY IF EXISTS "Allow users to read their notifications" ON public.user_notifications;
DROP POLICY IF EXISTS "Allow system to create notifications" ON public.user_notifications;
DROP POLICY IF EXISTS "Allow users to update their notifications" ON public.user_notifications;

CREATE POLICY "Allow users to read their notifications" ON public.user_notifications
  FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Allow system to create notifications" ON public.user_notifications
  FOR INSERT WITH CHECK (true); -- Allow system to create notifications

CREATE POLICY "Allow users to update their notifications" ON public.user_notifications
  FOR UPDATE USING (auth.uid()::text = user_id::text);

-- ===================================================================
-- STEP 3: ADD MISSING COLUMNS TO PROFILES TABLE
-- ===================================================================

-- Add any missing columns that might not exist in the current schema
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS location TEXT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS photos TEXT[] DEFAULT NULL,
ADD COLUMN IF NOT EXISTS personalityTraits JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS relationshipPreferences JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS boundaries TEXT[] DEFAULT NULL,
ADD COLUMN IF NOT EXISTS twin_context JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS allow_persona_learning_from_chats BOOLEAN DEFAULT NULL;

-- Add personality test result columns to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS extraversion_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS agreeableness_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS conscientiousness_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS neuroticism_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS openness_score INTEGER DEFAULT NULL,
ADD COLUMN IF NOT EXISTS personality_test_completed_at TIMESTAMPTZ DEFAULT NULL,
ADD COLUMN IF NOT EXISTS personality_insights JSONB DEFAULT NULL;

-- Add comments for clarity
COMMENT ON COLUMN public.profiles.extraversion_score IS 'Big Five Extraversion score (0-100)';
COMMENT ON COLUMN public.profiles.agreeableness_score IS 'Big Five Agreeableness score (0-100)';
COMMENT ON COLUMN public.profiles.conscientiousness_score IS 'Big Five Conscientiousness score (0-100)';
COMMENT ON COLUMN public.profiles.neuroticism_score IS 'Big Five Neuroticism score (0-100)';
COMMENT ON COLUMN public.profiles.openness_score IS 'Big Five Openness score (0-100)';
COMMENT ON COLUMN public.profiles.personality_test_completed_at IS 'Timestamp when personality test was last completed';
COMMENT ON COLUMN public.profiles.personality_insights IS 'JSON object containing personality insights and interpretations';

-- Create index for personality test queries
CREATE INDEX IF NOT EXISTS profiles_personality_test_idx ON public.profiles (personality_test_completed_at) WHERE personality_test_completed_at IS NOT NULL;

-- ===================================================================
-- STEP 4: ADD TEST DATA FOR DISCOVERY
-- ===================================================================

-- Add test profiles for discovery testing (only if they don't exist)
INSERT INTO public.profiles (
  id, 
  username, 
  full_name, 
  avatar_url, 
  age, 
  gender, 
  bio, 
  location,
  interests,
  communication_preferences,
  created_at,
  updated_at
) VALUES 
  -- Test Roberto variations for discovery testing
  ('550e8400-e29b-41d4-a716-446655440001', 'roberto_test1', 'Roberto Martinez', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e', 29, 'Male', 'Software developer from Spain. Love technology and meeting new people.', 'Madrid, Spain', '["technology", "programming", "travel", "food"]'::jsonb, '["text", "video_call"]'::text[], NOW(), NOW()),
  
  ('550e8400-e29b-41d4-a716-446655440002', 'roberto_test2', 'Roberto Silva', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d', 27, 'Male', 'Designer and entrepreneur from Brazil. Passionate about creativity and innovation.', 'São Paulo, Brazil', '["design", "business", "art", "music"]'::jsonb, '["text", "voice_call"]'::text[], NOW(), NOW()),
  
  -- Other test users with different names
  ('550e8400-e29b-41d4-a716-446655440003', 'alice_johnson', 'Alice Johnson', 'https://images.unsplash.com/photo-1494790108755-2616b612b830', 28, 'Female', 'Love hiking, reading, and good coffee. Looking for meaningful connections.', 'San Francisco, CA', '["hiking", "reading", "coffee", "nature"]'::jsonb, '["text", "video_call"]'::text[], NOW(), NOW()),
  
  ('550e8400-e29b-41d4-a716-446655440004', 'bob_smith', 'Bob Smith', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e', 32, 'Male', 'Software engineer by day, chef by night. Enjoy traveling and trying new cuisines.', 'New York, NY', '["cooking", "travel", "technology", "food"]'::jsonb, '["text", "voice_call"]'::text[], NOW(), NOW()),
  
  ('550e8400-e29b-41d4-a716-446655440005', 'carol_davis', 'Carol Davis', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80', 26, 'Female', 'Artist and yoga instructor. Passionate about creativity and mindfulness.', 'Los Angeles, CA', '["art", "yoga", "meditation", "creativity"]'::jsonb, '["text", "video_call"]'::text[], NOW(), NOW())

ON CONFLICT (id) DO NOTHING; -- Don't insert if ID already exists

-- ===================================================================
-- STEP 5: VERIFICATION
-- ===================================================================

-- Verify tables were created
SELECT 'TABLES VERIFICATION' as status;
SELECT tablename, schemaname FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE '%user%' ORDER BY tablename;

-- Verify test profiles were added
SELECT 'TEST PROFILES VERIFICATION' as status;
SELECT id, username, full_name, age, gender, location FROM public.profiles ORDER BY created_at DESC LIMIT 10;

-- Verify personality columns were added
SELECT 'PERSONALITY COLUMNS VERIFICATION' as status;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND table_schema = 'public'
AND (column_name LIKE '%personality%' OR column_name LIKE '%score')
ORDER BY ordinal_position;

SELECT 'ALL FIXES APPLIED SUCCESSFULLY! 🎉' as status;
