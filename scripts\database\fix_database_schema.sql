-- CRITICAL FIX: Add missing profile fields for user discovery
-- Run this in Supabase SQL Editor

-- 1. First, check current profiles table structure
\d public.profiles;

-- 2. Add missing critical fields for user discovery
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS is_discoverable BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS location TEXT,
ADD COLUMN IF NOT EXISTS photos TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS last_active TIMESTAMPTZ DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS age INTEGER,
ADD COLUMN IF NOT EXISTS gender TEXT,
ADD COLUMN IF NOT EXISTS bio TEXT,
ADD COLUMN IF NOT EXISTS personality_traits JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS communication_style JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS interests JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS relationship_preferences JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS boundaries JSONB DEFAULT '[]'::jsonb;

-- 3. Update existing profiles to be discoverable
UPDATE public.profiles 
SET 
  is_discoverable = TRUE,
  last_active = NOW()
WHERE is_discoverable IS NULL;

-- 4. Check current profiles state
SELECT 
  id, 
  username, 
  full_name, 
  email,
  is_discoverable,
  location,
  age,
  gender,
  array_length(photos, 1) as photo_count,
  created_at
FROM public.profiles 
ORDER BY created_at DESC;

-- 5. Verify discovery functionality
SELECT 
  COUNT(*) as total_profiles,
  COUNT(CASE WHEN is_discoverable = TRUE THEN 1 END) as discoverable_profiles,
  COUNT(CASE WHEN is_discoverable IS NULL THEN 1 END) as null_profiles,
  COUNT(CASE WHEN is_discoverable = FALSE THEN 1 END) as non_discoverable_profiles
FROM public.profiles;

-- 6. Show profiles that should be discoverable for testing
SELECT 
  id,
  username,
  full_name,
  email,
  is_discoverable,
  '✅ Should appear in discovery' as status
FROM public.profiles 
WHERE is_discoverable = TRUE
ORDER BY created_at DESC;
