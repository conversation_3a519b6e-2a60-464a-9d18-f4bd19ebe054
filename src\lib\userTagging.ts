import { supabase } from './supabase';
import { ErrorLogger } from './errorHandler';
import { UserInterestTag, ContactWillingnessStatus } from '../types';

/**
 * User Tagging Service
 * Handles interest tags, contact willingness, and related functionality
 */

// Tag Types with their display information
export const TAG_TYPES = {
  interested: {
    label: 'Interested',
    emoji: '👋',
    description: 'Show interest in connecting'
  },
  very_interested: {
    label: 'Very Interested',
    emoji: '💫',
    description: 'Strong interest in connecting'
  },
  potential_match: {
    label: 'Potential Match',
    emoji: '💖',
    description: 'See great potential for connection'
  }
} as const;

export type TagType = keyof typeof TAG_TYPES;

/**
 * Add or update an interest tag for a user
 */
export async function addInterestTag(
  taggedUserId: string,
  tagType: TagType,
  currentUserId: string
): Promise<{ success: boolean; error?: string; tag?: UserInterestTag }> {
  try {
    // Prevent self-tagging
    if (taggedUserId === currentUserId) {
      return { success: false, error: 'Cannot tag yourself' };
    }

    const tagData = {
      tagger_id: currentUserId,
      tagged_user_id: taggedUserId,
      tag_type: tagType,
      emoji: TAG_TYPES[tagType].emoji
    };

    const { data, error } = await supabase
      .from('user_interest_tags')
      .upsert(tagData, { onConflict: 'tagger_id,tagged_user_id' })
      .select('*')
      .single();

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    // Create notification for the tagged user
    await createTagNotification(taggedUserId, currentUserId, tagType);

    return { success: true, tag: data };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to add interest tag' };
  }
}

/**
 * Remove an interest tag
 */
export async function removeInterestTag(
  taggedUserId: string,
  currentUserId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('user_interest_tags')
      .delete()
      .eq('tagger_id', currentUserId)
      .eq('tagged_user_id', taggedUserId);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to remove interest tag' };
  }
}

/**
 * Get tags that a user has given to others
 */
export async function getUserTags(userId: string): Promise<UserInterestTag[]> {
  try {
    const { data, error } = await supabase
      .from('user_interest_tags')
      .select(`
        *,
        profiles!user_interest_tags_tagged_user_id_fkey(full_name)
      `)
      .eq('tagger_id', userId);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return [];
    }

    return data || [];
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return [];
  }
}

/**
 * Get tags that a user has received from others
 */
export async function getReceivedTags(userId: string): Promise<UserInterestTag[]> {
  try {
    const { data, error } = await supabase
      .from('user_interest_tags')
      .select(`
        *,
        profiles!user_interest_tags_tagger_id_fkey(full_name)
      `)
      .eq('tagged_user_id', userId);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return [];
    }

    return data || [];
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return [];
  }
}

/**
 * Get the tag that current user has given to a specific user
 */
export async function getUserTagForProfile(
  profileId: string,
  currentUserId: string
): Promise<UserInterestTag | null> {
  try {
    const { data, error } = await supabase
      .from('user_interest_tags')
      .select('*')
      .eq('tagger_id', currentUserId)
      .eq('tagged_user_id', profileId)
      .single();

    if (error && error.code !== 'PGRST116') {
      ErrorLogger.logError(error, 'database');
      return null;
    }

    return data || null;
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return null;
  }
}

/**
 * Set contact willingness status
 */
export async function setContactWillingness(
  userId: string,
  isOpen: boolean,
  statusMessage?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('contact_willingness_status')
      .upsert({
        user_id: userId,
        is_open_to_contact: isOpen,
        status_message: statusMessage,
        updated_at: new Date().toISOString()
      }, { onConflict: 'user_id' });

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to update contact willingness' };
  }
}

/**
 * Get contact willingness status for a user
 */
export async function getContactWillingness(userId: string): Promise<ContactWillingnessStatus | null> {
  try {
    const { data, error } = await supabase
      .from('contact_willingness_status')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      ErrorLogger.logError(error, 'database');
      return null;
    }

    return data || null;
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return null;
  }
}

/**
 * Create a notification for a tag event
 */
async function createTagNotification(
  taggedUserId: string,
  taggerId: string,
  tagType: TagType
): Promise<void> {
  try {
    // Get tagger's name
    const { data: taggerProfile } = await supabase
      .from('profiles')
      .select('full_name')
      .eq('id', taggerId)
      .single();

    const taggerName = taggerProfile?.full_name || 'Someone';
    const tagInfo = TAG_TYPES[tagType];

    await supabase
      .from('user_notifications')
      .insert({
        user_id: taggedUserId,
        type: 'interest_tag',
        title: 'New Interest Tag!',
        message: `${taggerName} tagged you as "${tagInfo.label}" ${tagInfo.emoji}`,
        data: {
          tagger_id: taggerId,
          tag_type: tagType,
          tagger_name: taggerName
        }
      });
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    // Don't throw - notification failure shouldn't break tagging
  }
}

/**
 * Get tag statistics for a user (how many of each type they've received)
 */
export async function getTagStats(userId: string): Promise<Record<TagType, number>> {
  try {
    const { data, error } = await supabase
      .from('user_interest_tags')
      .select('tag_type')
      .eq('tagged_user_id', userId);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { interested: 0, very_interested: 0, potential_match: 0 };
    }

    const stats = { interested: 0, very_interested: 0, potential_match: 0 };
    data?.forEach(tag => {
      if (tag.tag_type in stats) {
        stats[tag.tag_type as TagType]++;
      }
    });

    return stats;
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { interested: 0, very_interested: 0, potential_match: 0 };
  }
}
