// Add to existing types
export interface Reaction {
  type: string;
  userId: string;
}

export interface DigitalTwin {
  id: string;
  name: string;
  personalityTraits?: (string | PersonalityTrait)[];
  interests?: (string | Interest)[];
  avatar?: string;
  age?: number;
  gender?: string;
  bio?: string;
  communicationStyle?: string[];
  relationshipGoals?: string[];
  compatibilityScore?: number;
}

export interface Message {
  id: string;
  conversationId: string;
  sender: 'user' | 'twin' | 'real_user';
  content: string;
  timestamp: Date;
  reactions: Reaction[];
  senderId?: string; // ID of the real user who sent the message
  status?: 'sending' | 'sent' | 'failed'; // Added message status
}

// Define structured types for profile attributes
export interface PersonalityTrait {
  id: string;
  name: string;
  value: number;
}

export interface Interest {
  id: string;
  name: string;
  category: string;
}

export interface RelationshipPreference {
  id: string;
  type: string;
  importance: number;
}

export interface UserInterestTag {
  id: string;
  tagger_id: string;
  tagged_user_id: string;
  tag_type: 'interested' | 'very_interested' | 'potential_match';
  emoji?: string;
  created_at: string;
  tagger_name?: string; // For display purposes
}

export interface ContactWillingnessStatus {
  id: string;
  user_id: string;
  is_open_to_contact: boolean;
  status_message?: string;
  updated_at: string;
}

export interface UserNotification {
  id: string;
  user_id: string;
  type: 'interest_tag' | 'contact_willingness' | 'mutual_interest';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  created_at: string;
}

export interface ConversationBoundaries {
  id: string;
  user_id: string;
  allow_messages_from: 'everyone' | 'tagged_only' | 'mutual_interest' | 'none';
  require_introduction: boolean;
  auto_decline_explicit: boolean;
  response_time_expectation: 'immediate' | 'within_hours' | 'within_day' | 'flexible';
  availability_window_start?: string; // Time format: "HH:MM"
  availability_window_end?: string;   // Time format: "HH:MM"
  availability_timezone: string;
  block_explicit_content: boolean;
  block_personal_questions_early: boolean;
  block_meeting_requests_early: boolean;
  block_contact_sharing_early: boolean;
  custom_boundaries: string[];
  created_at: string;
  updated_at: string;
}

export interface BlockedUser {
  id: string;
  blocker_id: string;
  blocked_id: string;
  blocked_user?: Profile; // Populated when fetching
  reason?: string;
  created_at: string;
}

export interface ConversationReport {
  id: string;
  reporter_id: string;
  reported_user_id: string;
  reported_user?: Profile; // Populated when fetching
  conversation_id?: string;
  report_type: 'harassment' | 'inappropriate_content' | 'spam' | 'fake_profile' | 'other';
  description?: string;
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  created_at: string;
  updated_at: string;
}

export interface Profile {
  id: string;
  name: string;
  avatar?: string;
  avatar_url?: string; // Added to support both avatar and avatar_url
  bio?: string;
  location?: string;
  photos?: string[];
  
  // Changed from string[] to structured object arrays
  personalityTraits?: PersonalityTrait[];
  interests?: Interest[];
  relationshipPreferences?: RelationshipPreference[];
  
  // Keep these as string arrays since they're simpler
  values?: string[];
  preferredConversationTopics?: string[];
  communicationPreferences?: string[];
  boundaries?: string[];
  
  twin_context?: TwinContext | null;
  
  allow_persona_learning_from_chats?: boolean;
  
  age?: number;
  gender?: string;

  // Personality test fields
  personality_test_completed_at?: string;
  extraversion_score?: number;
  agreeableness_score?: number;
  conscientiousness_score?: number;
  neuroticism_score?: number;
  openness_score?: number;

  createdAt: Date | string;
  updatedAt: Date | string;
  username?: string;

  // Tagging system fields
  isOpenToContact?: boolean;
  contactStatusMessage?: string;
  interestTags?: UserInterestTag[];
  receivedTags?: UserInterestTag[];
}

export interface TwinContext {
  learned_preferences?: string[];
  recent_topics_discussed?: string[];
  communication_style_notes?: string[];
  key_facts_mentioned_by_user?: string[];
  sentiment_history?: string[]; // e.g., last 5 sentiments
}

export interface Conversation {
  id: string;
  userId: string;
  digitalTwin?: DigitalTwin; // Optional now since conversation can be with real user
  realUser?: Profile; // Added for real user conversations
  messages: Message[];
  createdAt: Date;
  lastActive: Date;
  engagementScore: number;
  type: 'twin' | 'real_user'; // Added to distinguish conversation types
}

// Milestone type for conversation milestones
export interface Milestone {
  type: MilestoneType;
  description: string;
  timestamp: Date;
}

// Enum for milestone types
export enum MilestoneType {
  FIRST_MESSAGE = 'FIRST_MESSAGE',
  SHARED_INTEREST = 'SHARED_INTEREST',
  DEEP_QUESTION = 'DEEP_QUESTION',
  PERSONAL_STORY = 'PERSONAL_STORY',
  CONTACT_INFO = 'CONTACT_INFO',
  REAL_WORLD_PLAN = 'REAL_WORLD_PLAN',
}

// User and Authentication Types (if not already defined elsewhere)
export interface User {
  id: string;
  email: string;
  name?: string;
  createdAt: Date;
  updatedAt: Date;
}