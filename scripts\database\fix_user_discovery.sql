-- Fix existing user profiles to be discoverable
-- Run this in Supabase SQL Editor

-- First, let's see the current state of profiles
SELECT 
  id, 
  username, 
  full_name, 
  is_discoverable,
  created_at
FROM public.profiles 
ORDER BY created_at DESC;

-- Update all profiles with NULL is_discoverable to TRUE
UPDATE public.profiles 
SET is_discoverable = TRUE 
WHERE is_discoverable IS NULL;

-- Verify the update
SELECT 
  id, 
  username, 
  full_name, 
  is_discoverable,
  created_at,
  CASE 
    WHEN is_discoverable IS NULL THEN '❌ NULL' 
    WHEN is_discoverable = TRUE THEN '✅ TRUE' 
    ELSE '❌ FALSE' 
  END as status
FROM public.profiles 
ORDER BY created_at DESC;

-- Show final count
SELECT 
  COUNT(*) as total_profiles,
  COUNT(CASE WHEN is_discoverable = TRUE THEN 1 END) as discoverable_profiles,
  COUNT(CASE WHEN is_discoverable IS NULL THEN 1 END) as null_profiles,
  COUNT(CASE WHEN is_discoverable = FALSE THEN 1 END) as non_discoverable_profiles
FROM public.profiles;
