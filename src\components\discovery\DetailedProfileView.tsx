import React, { useState, useCallback } from 'react';
import { Brain, Heart, MessageCircle, MessageSquare, Sparkles, UserCircle, XCircle, Lightbulb, Loader2, Copy, X as XIcon } from 'lucide-react';
import { Profile } from '../../types';
import { generateIcebreakerSuggestion } from '../../lib/llm'; // Updated to generic LLM file
import { useProfile } from '../../context/ProfileContext'; // Added
import { useToast } from '../../hooks/useToast'; // Added
import InterestTagButton from '../tagging/InterestTagButton';
import UserBadges, { DetailedInterestBadges } from '../tagging/UserBadges';
import BlockUserButton from '../settings/BlockUserButton';

/**
 * @file DetailedProfileView.tsx
 * @description Modal component to display a detailed view of a user's profile.
 * It includes comprehensive information like bio, interests, values, etc.,
 * and provides actions to start a chat or close the view.
 */

interface DetailedProfileViewProps {
  /** The profile data to display in detail. */
  profile: Profile;
  onStartChat: (profile: Profile) => void;
  onClose: () => void;
}

const DetailedProfileView: React.FC<DetailedProfileViewProps> = ({ profile, onStartChat, onClose }) => {
  const { profile: currentUserProfile } = useProfile(); // Current authenticated user's profile, used for personalization.
  const { showSuccess, showError, showLoading, dismiss } = useToast(); // For user feedback.

  // --- Icebreaker Feature State ---
  /** Stores the AI-generated icebreaker suggestion string. Null if no suggestion is available or if it's cleared. */
  const [icebreakerSuggestion, setIcebreakerSuggestion] = useState<string | null>(null);
  /** Boolean state indicating if an icebreaker suggestion is currently being generated. Used for loading UI. */
  const [isGeneratingSuggestion, setIsGeneratingSuggestion] = useState(false);
  /** Stores any error message encountered during icebreaker generation. Null if no error. */
  const [suggestionError, setSuggestionError] = useState<string | null>(null);

  /**
   * Handles the request to generate an icebreaker suggestion.
   * It calls the `generateIcebreakerSuggestion` API, manages loading states,
   * and updates the UI with the suggestion or an error message.
   */
  const handleGetIcebreaker = useCallback(async () => {
    if (!currentUserProfile) {
      showError("Your profile isn't available to generate an icebreaker.");
      return;
    }
    if (!profile) { // Target profile
      showError("Target profile data is missing.");
      return;
    }

    setIsGeneratingSuggestion(true);
    setSuggestionError(null);
    setIcebreakerSuggestion(null); // Clear previous suggestion
    const loadingToastId = showLoading('Your Digital Twin is crafting an icebreaker...');

    try {
          const params = {
      currentUserTwinName: `${currentUserProfile.name}'s Twin`,
      currentUserInterests: currentUserProfile.interests?.map(i => i.name) || [],
      viewedProfileName: profile.name,
      viewedProfileInterests: profile.interests?.map(i => i.name) || [],
      viewedProfileBio: profile.bio || '',
    };
      const suggestion = await generateIcebreakerSuggestion(params);
      dismiss(loadingToastId);
      if (suggestion) {
        setIcebreakerSuggestion(suggestion);
        showSuccess('Icebreaker suggestion generated!');
      } else {
        setSuggestionError('Could not generate a suggestion. The twin might be out of ideas!');
        showError('Failed to get an icebreaker suggestion.');
      }
    } catch (err) {
      dismiss(loadingToastId);
      console.error("Error generating icebreaker:", err);
      setSuggestionError('An error occurred while generating the suggestion.');
      showError('An error occurred while generating the icebreaker.');
    } finally {
      setIsGeneratingSuggestion(false);
    }
  }, [currentUserProfile, profile, showLoading, dismiss, showSuccess, showError]); // Dependencies for useCallback

  /**
   * Copies the current icebreaker suggestion to the user's clipboard.
   * Provides user feedback via toast notifications.
   */
  const handleCopySuggestion = () => {
    if (icebreakerSuggestion) {
      navigator.clipboard.writeText(icebreakerSuggestion)
        .then(() => showSuccess('Icebreaker copied to clipboard!'))
        .catch((err) => {
          console.error("Failed to copy icebreaker: ", err); // Keep console.error for debugging copy issues
          showError('Failed to copy icebreaker.');
        });
    }
  };

  /**
   * Dismisses the currently displayed icebreaker suggestion or error message.
   */
  const handleDismissSuggestion = () => {
    setIcebreakerSuggestion(null);
    setSuggestionError(null);
  };
  
  /**
   * Helper function to render a section with a title, icon, and a list of items as tags.
   * If the items array is empty or undefined, the section is not rendered.
   * @param title - The title of the section.
   * @param items - An array of strings to display as tags.
   * @param icon - Optional React node (e.g., an icon) to display next to the title.
   * @returns JSX for the section or null if items are not provided.
   */
  const renderSection = (title: string, items: string[] | undefined, icon?: React.ReactNode) => {
    if (!items || items.length === 0) {
      return null; // Do not render the section if there are no items
    }
    return (
      <div className="mb-6"> {/* Outer margin for the section */}
        <div className="flex items-center mb-2">
          {icon && <span className="mr-2">{icon}</span>}
          <h3 className="text-lg font-semibold text-gray-700">{title}</h3>
        </div>
        <div className="flex flex-wrap gap-2">
          {items.map((item, index) => (
            <span key={index} className="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-full">
              {item}
            </span>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-auto my-8 p-6 md:p-8 relative transform transition-all duration-300 max-h-[90vh] overflow-y-auto">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 transition-colors"
          aria-label="Close profile view"
        >
          <XCircle className="w-8 h-8" />
        </button>

        {/* Header: Avatar, Name, Age/Gender */}
        <div className="flex flex-col items-center mb-6 text-center">
          {profile.avatar_url ? (
            <img
              src={profile.avatar_url}
              alt={`${profile.name}'s avatar`}
              className="w-32 h-32 rounded-full object-cover border-4 border-purple-300 shadow-lg mb-4"
            />
          ) : (
            <div className="w-32 h-32 rounded-full bg-gray-300 flex items-center justify-center border-4 border-purple-300 shadow-lg mb-4">
              <UserCircle className="w-24 h-24 text-gray-500" />
              {/* <span className="text-5xl font-semibold text-gray-600">{getInitials(profile.name)}</span> */}
            </div>
          )}
          <h1 className="text-3xl font-bold text-gray-800">{profile.name || 'Unnamed User'}</h1>
          {(profile.age || profile.gender) && (
            <p className="text-md text-gray-600 mt-1">
              {profile.age && <span>{profile.age} years old</span>}
              {profile.age && profile.gender && <span> &bull; </span>}
              {profile.gender && <span>{profile.gender}</span>}
            </p>
          )}
        </div>

        {/* User Badges and Interest Tags */}
        <div className="mb-6">
          <div className="flex flex-col items-center gap-4">
            {/* Display badges showing interest from others and contact status */}
            <UserBadges
              userId={profile.id}
              size="md"
              className="justify-center"
            />

            {/* Detailed interest breakdown */}
            <DetailedInterestBadges
              userId={profile.id}
              size="sm"
              className="justify-center"
            />

            {/* Interest tagging button */}
            <InterestTagButton
              profileId={profile.id}
              profileName={profile.name}
              size="md"
            />
          </div>
        </div>

        {/* Full Bio */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2 flex items-center">
            <UserCircle className="w-5 h-5 mr-2 text-purple-500" /> About Me
          </h3>
          <p className="text-gray-600 whitespace-pre-wrap leading-relaxed">
            {profile.bio || <span className="italic">No bio provided.</span>}
          </p>
        </div>        {/* Interests */}
        {renderSection("Interests", profile.interests?.map(i => typeof i === 'string' ? i : i.name), <Sparkles className="w-5 h-5 text-purple-500" />)}
        
        {/* Values - Assuming 'values' is a field in Profile, like interests */}
        {renderSection("Values", profile.values, <Heart className="w-5 h-5 text-purple-500" />)}

        {/* Preferred Conversation Topics */}
        {renderSection("Preferred Conversation Topics", profile.preferredConversationTopics, <MessageCircle className="w-5 h-5 text-purple-500" />)}

        {/* Personality Traits (Placeholder/Example) */}
        {renderSection("Personality Traits", profile.personalityTraits?.map(trait => trait.name), <Brain className="w-5 h-5 text-purple-500" />)}


        {/* --- Icebreaker Suggestion Section --- */}
        {/* This section allows users to generate AI-powered icebreaker messages */}
        {/* based on their profile and the viewed profile. */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-semibold text-gray-700 flex items-center">
              <Lightbulb className="w-5 h-5 mr-2 text-yellow-500" />
              Digital Twin Icebreaker
            </h3>
            {/* Button to trigger icebreaker generation */}
            <button
              onClick={handleGetIcebreaker}
              disabled={isGeneratingSuggestion}
              className="px-4 py-2 text-sm font-medium rounded-lg text-purple-700 bg-purple-100 hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isGeneratingSuggestion ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" /> // Loading spinner
              ) : (
                <Lightbulb className="w-4 h-4 mr-2" /> // Default icon
              )}
              Get Suggestion
            </button>
          </div>

          {/* Conditional rendering for loading, error, or suggestion display */}
          {isGeneratingSuggestion && (
            // Loading state display
            <div className="p-4 bg-gray-50 rounded-lg text-center">
              <Loader2 className="w-8 h-8 mx-auto text-purple-500 animate-spin mb-2" />
              <p className="text-sm text-gray-600">Your Digital Twin is thinking...</p>
            </div>
          )}

          {suggestionError && !isGeneratingSuggestion && (
            // Error state display
            <div className="p-4 bg-red-50 rounded-lg text-red-700">
              <p className="text-sm mb-2">{suggestionError}</p>
              <button
                onClick={handleGetIcebreaker} // Allows user to retry
                className="px-3 py-1 text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Try Again
              </button>
            </div>
          )}

          {icebreakerSuggestion && !isGeneratingSuggestion && !suggestionError && (
            // Success state: Display suggestion and action buttons (Copy, Dismiss)
            <div className="p-4 bg-green-50 rounded-lg">
              <p className="text-sm text-green-800 mb-3 leading-relaxed">"{icebreakerSuggestion}"</p>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={handleCopySuggestion}
                  className="p-2 text-gray-500 hover:text-purple-600 transition-colors"
                  title="Copy suggestion"
                >
                  <Copy className="w-5 h-5" />
                </button>
                <button
                  onClick={handleDismissSuggestion}
                  className="p-2 text-gray-500 hover:text-red-600 transition-colors"
                  title="Dismiss suggestion"
                >
                  <XIcon className="w-5 h-5" />
                </button>
              </div>
            </div>
          )}
        </div>
        
        {/* Action Buttons (Start Chat, Block/Report, Close) */}
        <div className="mt-8 pt-6 border-t border-gray-200 flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-4">
          <div className="flex justify-start">
            <BlockUserButton
              userId={profile.id}
              userName={profile.name}
              variant="dropdown"
              size="md"
            />
          </div>

          <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
            <button
              onClick={onClose}
              className="px-6 py-3 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
            >
              Close
            </button>
            <button
              onClick={() => onStartChat(profile)}
              className="px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-purple-600 to-pink-500 hover:from-purple-700 hover:to-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 flex items-center justify-center transition-all duration-150 ease-in-out shadow-md hover:shadow-lg"
            >
              <MessageSquare className="w-5 h-5 mr-2" />
              Start Chat
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailedProfileView;
