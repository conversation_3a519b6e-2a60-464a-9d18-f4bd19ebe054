// Quick test to check if tagging system tables exist
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testTaggingTables() {
  console.log('Testing tagging system tables...');
  
  try {
    // Test if user_interest_tags table exists
    const { data: tags, error: tagsError } = await supabase
      .from('user_interest_tags')
      .select('*')
      .limit(1);
    
    if (tagsError) {
      console.error('❌ user_interest_tags table error:', tagsError.message);
    } else {
      console.log('✅ user_interest_tags table accessible');
    }

    // Test if contact_willingness_status table exists
    const { data: willingness, error: willingnessError } = await supabase
      .from('contact_willingness_status')
      .select('*')
      .limit(1);
    
    if (willingnessError) {
      console.error('❌ contact_willingness_status table error:', willingnessError.message);
    } else {
      console.log('✅ contact_willingness_status table accessible');
    }

    // Test if user_notifications table exists
    const { data: notifications, error: notificationsError } = await supabase
      .from('user_notifications')
      .select('*')
      .limit(1);
    
    if (notificationsError) {
      console.error('❌ user_notifications table error:', notificationsError.message);
    } else {
      console.log('✅ user_notifications table accessible');
    }

    // Test current user session
    const { data: session, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      console.error('❌ Session error:', sessionError.message);
    } else if (session?.session) {
      console.log('✅ User authenticated:', session.session.user.email);
    } else {
      console.log('ℹ️ No active session - testing requires login');
    }

  } catch (error) {
    console.error('❌ General error:', error);
  }
}

testTaggingTables();
