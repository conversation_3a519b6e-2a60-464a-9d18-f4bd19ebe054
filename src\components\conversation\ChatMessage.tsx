import React, { useState } from 'react';
import { Heart, ThumbsUp, ThumbsDown, AlertTriangle, Check, Clock } from 'lucide-react';
import { Message } from '../../types';

interface ChatMessageProps {
  message: Message;
  participantName?: string;
  participantAvatar?: string;
  currentUserAvatar?: string;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ 
  message, 
  participantName = 'User', 
  participantAvatar,
  currentUserAvatar 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const isUser = message.sender === 'user';
    return (
    <div 
      className={`mb-4 flex ${isUser ? 'justify-end' : 'justify-start'} items-start space-x-3`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Avatar for non-user messages */}
      {!isUser && (
        <div className="flex-shrink-0">
          {participantAvatar ? (
            <img
              src={participantAvatar}
              alt={participantName}
              className="w-8 h-8 rounded-full object-cover"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white text-xs font-bold">
              {participantName?.charAt(0) || '?'}
            </div>
          )}
        </div>
      )}

      <div className={`max-w-[75%] relative ${isUser ? 'order-first' : ''}`}>
        {!isUser && participantName && (
          <div className="font-medium text-xs text-gray-500 mb-1 ml-2">
            {participantName}
          </div>
        )}
        
        <div 
          className={`rounded-2xl py-3 px-4 ${
            isUser 
              ? 'bg-gradient-to-r from-purple-600 to-pink-500 text-white' 
              : 'bg-white border border-gray-200 text-gray-800'
          }`}
        >
          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
        </div>
        
        <div className={`text-xs text-gray-500 mt-1 ml-2 flex items-center ${message.status === 'failed' ? 'text-red-500' : ''}`}>
          {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          {isUser && message.status === 'sending' && (
            <Clock className="h-3 w-3 ml-1 animate-spin" />
          )}
          {isUser && message.status === 'sent' && (
            <Check className="h-3 w-3 ml-1 text-blue-500" />
          )}
          {isUser && message.status === 'failed' && (
            <AlertTriangle className="h-3 w-3 ml-1 text-red-500" />
          )}
        </div>
        {isUser && message.status === 'failed' && (
           <p className="text-xs text-red-500 ml-2">Failed to send.</p>
        )}
        
        {!isUser && message.sender === 'twin' && isHovered && (
          <div className="absolute -bottom-3 right-0 flex space-x-1 bg-white rounded-full shadow-md py-1 px-2 animate-fadeIn">
            <button 
              className="text-gray-500 hover:text-pink-500 transition-colors" 
              aria-label="React with heart"
            >
              <Heart className="h-4 w-4" />
            </button>
            <button 
              className="text-gray-500 hover:text-green-500 transition-colors" 
              aria-label="React with thumbs up"
            >
              <ThumbsUp className="h-4 w-4" />
            </button>
            <button 
              className="text-gray-500 hover:text-red-500 transition-colors" 
              aria-label="React with thumbs down"
            >
              <ThumbsDown className="h-4 w-4" />
            </button>
          </div>
        )}
        
        {/* Display reactions */}
        {Array.isArray(message.reactions) && message.reactions.length > 0 && (
          <div className="flex mt-1 space-x-1 justify-end">
            {message.reactions.map((reaction, index) => (
              <span 
                key={`${reaction.type}-${reaction.userId}-${index}`}
                className="bg-gray-100 rounded-full px-2 py-1 text-xs flex items-center"
              >
                {reaction.type === 'heart' && '❤️'}
                {reaction.type === 'thumbsUp' && '👍'}
                {reaction.type === 'thumbsDown' && '👎'}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Avatar for user messages */}
      {isUser && (
        <div className="flex-shrink-0">
          {currentUserAvatar ? (
            <img
              src={currentUserAvatar}
              alt="You"
              className="w-8 h-8 rounded-full object-cover"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-pink-500 flex items-center justify-center text-white text-xs font-bold">
              U
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ChatMessage;