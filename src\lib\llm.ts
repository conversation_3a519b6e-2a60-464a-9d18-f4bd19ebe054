import { getUserFacts } from './dataManagement';

// DeepSeek LLM integration only
const getEnv = (key: string): string | undefined => {
  if (typeof import.meta !== 'undefined' && import.meta.env && key in import.meta.env) {
    return import.meta.env[key as keyof ImportMetaEnv] as string | undefined;
  }
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key];
  }
  return undefined;
};

const DEEPSEEK_API_KEY = getEnv('VITE_DEEPSEEK_API_KEY') || '';
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1';

if (!DEEPSEEK_API_KEY) {
  throw new Error('Missing DeepSeek API key. Please set VITE_DEEPSEEK_API_KEY in your .env file.');
}

async function deepseekChatCompletion({
  messages,
  model = 'deepseek-chat',
  temperature = 0.7,
  max_tokens = 150
}: {
  messages: { role: 'system' | 'user' | 'assistant'; content: string }[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}) {
  const res = await fetch(`${DEEPSEEK_API_URL}/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
    },
    body: JSON.stringify({
      model,
      messages,
      temperature,
      max_tokens
    })
  });
  if (!res.ok) {
    const txt = await res.text();
    throw new Error(`DeepSeek error: ${res.status} – ${txt}`);
  }
  const data = await res.json();
  return data.choices?.[0]?.message?.content ?? '';
}

export async function generateTwinResponse(
  _twinId: string,
  message: string,
  context: Array<string | { role: 'system' | 'user' | 'assistant'; content: string }>,
  twinData: { name: string; personality: string[]; interests: string[]; communicationStyle: string[]; chatHistory?: string[]; twin_context?: any; },
  userId?: string // <-- add userId for memory
): Promise<string> {
  if (!twinData.name) throw new Error('Twin name is required');
  let systemPrompt = `You are ${twinData.name}. Traits: ${twinData.personality.join(', ')}. Interests: ${twinData.interests.join(', ')}. Style: ${twinData.communicationStyle.join(', ')}.`;
  if (twinData.chatHistory) systemPrompt += ` History:\n${twinData.chatHistory.join('\n')}`;
  if (twinData.twin_context && Object.keys(twinData.twin_context).length) {
    const ctx = typeof twinData.twin_context === 'string' ? twinData.twin_context : JSON.stringify(twinData.twin_context);
    systemPrompt += `\nAdditional context about your persona evolution: ${ctx}`;
  }
  // Inject persistent user facts if userId is provided
  if (userId) {
    const facts = await getUserFacts(userId);
    if (facts.length) {
      systemPrompt += `\nUser persistent facts: ${facts.join('; ')}`;
    }
  }
  systemPrompt += `\nInstructions:\n1. Never mention you're an AI.\n2. Stay in character.\n3. Use empathy and shared interests.\n4. Be natural.\n5. Emojis sparingly.`;
  const messages = context.map(msg =>
    typeof msg === 'string'
      ? { role: 'user' as const, content: msg }
      : { role: (msg.role as 'system' | 'user' | 'assistant'), content: msg.content }
  );
  const payload: { role: 'system' | 'user' | 'assistant'; content: string }[] = [
    { role: 'system', content: systemPrompt },
    ...messages,
    { role: 'user', content: message }
  ];
  return deepseekChatCompletion({ messages: payload });
}

export async function verifyLlmConnection(): Promise<boolean> {
  try {
    const content = await deepseekChatCompletion({
      messages: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Ping' }
      ],
      max_tokens: 5
    });
    return !!content;
  } catch {
    return false;
  }
}

export async function generateIcebreakerSuggestion(params: {
  currentUserTwinName: string;
  currentUserInterests?: string[];
  viewedProfileName: string;
  viewedProfileInterests?: string[];
  viewedProfileBio?: string;
}): Promise<string | null> {
  const { currentUserTwinName, currentUserInterests = [], viewedProfileName, viewedProfileInterests = [], viewedProfileBio = '' } = params;
  const prompt = `You are ${currentUserTwinName}. Craft a concise icebreaker for ${viewedProfileName}. User interests: ${currentUserInterests.join(', ') || 'None'}. Profile interests: ${viewedProfileInterests.join(', ') || 'None'}. Bio: "${viewedProfileBio || 'N/A'}".`;
  try {
    return await deepseekChatCompletion({
      messages: [
        { role: 'system', content: prompt },
        { role: 'user', content: prompt }
      ],
      temperature: 0.75,
      max_tokens: 80
    });
  } catch {
    return null;
  }
}