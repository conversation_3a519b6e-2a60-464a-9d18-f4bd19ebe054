import React, { useEffect, useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useProfile } from '../../context/ProfileContext';
import { supabase } from '../../lib/supabase';
import { browseProfiles } from '../../lib/profile';

interface ProfileRecord {
  id: string;
  username: string;
  full_name: string | null;
  avatar_url: string | null;
  created_at: string;
  email?: string;
}

export const UserDebugInfo: React.FC = () => {
  const { user } = useAuth();
  const { profile } = useProfile();
  const [allProfiles, setAllProfiles] = useState<ProfileRecord[]>([]);
  const [browseResult, setBrowseResult] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDebugData = async () => {
      try {
        // Fetch all profiles directly from database
        const { data, error } = await supabase
          .from('profiles')
          .select('id, username, full_name, avatar_url, created_at')
          .order('created_at', { ascending: false });

        if (error) throw error;
        setAllProfiles(data || []);

        // Test browseProfiles function if user is available
        if (user?.id) {
          console.log('Testing browseProfiles with user ID:', user.id);
          const browseResult = await browseProfiles({}, { page: 1, pageSize: 10 }, user.id);
          console.log('browseProfiles result:', browseResult);
          setBrowseResult(browseResult);
        }
      } catch (error) {
        console.error('Error fetching debug data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDebugData();
  }, [user?.id]);

  if (loading) return <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg">Loading debug info...</div>;

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-lg max-h-96 overflow-y-auto z-50">
      <h3 className="font-bold text-lg mb-3 text-gray-800">Debug Info</h3>
      
      <div className="mb-4">
        <h4 className="font-semibold text-green-600">Current User:</h4>
        <p className="text-sm">ID: {user?.id}</p>
        <p className="text-sm">Email: {user?.email}</p>
        <p className="text-sm">Profile Name: {profile?.name}</p>
        <p className="text-sm">Username: {profile?.username}</p>
      </div>

      <div className="mb-4">
        <h4 className="font-semibold text-blue-600 mb-2">All Profiles ({allProfiles.length}):</h4>
        {allProfiles.map((p, index) => (
          <div key={p.id} className={`text-xs mb-2 p-2 rounded ${p.id === user?.id ? 'bg-yellow-100' : 'bg-gray-50'}`}>
            <div className="font-medium">#{index + 1} {p.id === user?.id ? '(YOU)' : ''}</div>
            <div>ID: {p.id}</div>
            <div>Username: {p.username}</div>
            <div>Name: {p.full_name || 'N/A'}</div>
            <div>Created: {new Date(p.created_at).toLocaleString()}</div>
          </div>
        ))}
      </div>

      <div>
        <h4 className="font-semibold text-purple-600 mb-2">Browse Profiles Result:</h4>
        {browseResult ? (
          <div className="text-xs">
            <div>Total Count: {browseResult.totalCount}</div>
            <div>Profiles Found: {browseResult.profiles?.length || 0}</div>
            {browseResult.profiles?.map((p: any, index: number) => (
              <div key={p.id} className="mb-1 p-1 bg-purple-50 rounded">
                <div>#{index + 1}: {p.name} (ID: {p.id})</div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-xs text-gray-500">No browse result yet</div>
        )}
      </div>
    </div>
  );
};
