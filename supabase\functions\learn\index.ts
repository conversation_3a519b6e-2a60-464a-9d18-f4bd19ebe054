// deno-lint-ignore-file
// deno-fmt-ignore-file
// @ts-nocheck
/*
 * Edge Function: supabase/functions/learn/index.ts
 * Keeps the twin persona (twin_context) up‑to‑date after every new message.
 * Now uses DeepSeek for completions (removes <PERSON>wen logic).
 */

import { createClient } from "https://esm.sh/@supabase/supabase-js@2.43.1";
import { serve } from "https://deno.land/std@0.207.0/http/server.ts";
import type { Database } from "../_shared/database.types.ts";

/* -------------------------------------------------- */
/* Environment                                        */
/* -------------------------------------------------- */
const supabase = createClient<Database>(
  Deno.env.get("SUPABASE_URL")!,
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!,
  { auth: { persistSession: false } }
);

const DEEPSEEK_API_KEY = Deno.env.get("DEEPSEEK_API_KEY") ?? '';
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1';

if (!DEEPSEEK_API_KEY) console.warn("[learn] DEEPSEEK_API_KEY env var is missing");

/* -------------------------------------------------- */
/* Helper: call DeepSeek chat‑completions              */
/* -------------------------------------------------- */
async function deepseekCompletion(prompt: string): Promise<string> {
  const res = await fetch(`${DEEPSEEK_API_URL}/chat/completions`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${DEEPSEEK_API_KEY}`
    },
    body: JSON.stringify({
      model: "deepseek-chat",
      messages: [{ role: "user", content: prompt }]
    })
  });
  if (!res.ok) {
    const txt = await res.text();
    throw new Error(`DeepSeek error: ${res.status} – ${txt}`);
  }
  const data = await res.json();
  return data.choices?.[0]?.message?.content ?? "";
}

/* -------------------------------------------------- */
/* Function handler                                   */
/* -------------------------------------------------- */
serve(async (req: Request) => {
  try {
    const { conversation_id, message } = await req.json();

    /* 1 | Fetch conversation + owner */
    const { data: convo, error } = await supabase
      .from("conversations")
      .select("owner:owner (id, twin_context, allow_persona_learning_from_chats)")
      .eq("id", conversation_id)
      .maybeSingle();

    if (error || !convo || !convo.owner) {
      return new Response("conversation or owner not found", { status: 404 });
    }

    /* 1.1 | Check if persona learning is allowed */
    const learningAllowed = convo.owner.allow_persona_learning_from_chats !== false;
    if (!learningAllowed) {
      return new Response(
        JSON.stringify({ message: "Persona learning is disabled for this user." }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    }

    /* 2 | Initialize twin_context if null/empty and prepare for DeepSeek update */
    let currentTwinContext = convo.owner.twin_context;
    if (!currentTwinContext || Object.keys(currentTwinContext).length === 0) {
      currentTwinContext = {
        learned_preferences: [],
        recent_topics_discussed: [],
        communication_style_notes: [],
        key_facts_mentioned_by_user: [],
        sentiment_history: []
      };
    }

    const userMessage = message;
    // Compose prompt for DeepSeek
    const prompt = `Given the following user message and current persona context, update the persona context to reflect new preferences, topics, communication style, and key facts.\n\nCurrent context: ${JSON.stringify(currentTwinContext)}\n\nUser message: ${userMessage}\n\nReturn only the updated context as a JSON object.`;
    const completion = await deepseekCompletion(prompt);
    let updatedContext;
    try {
      updatedContext = JSON.parse(completion);
    } catch {
      updatedContext = currentTwinContext;
    }
    // Save updated context
    await supabase.from("profiles").update({ twin_context: updatedContext }).eq("id", convo.owner.id);
    return new Response(JSON.stringify({ twin_context: updatedContext }), { status: 200, headers: { "Content-Type": "application/json" } });
  } catch (err) {
    return new Response(`Error: ${err}`, { status: 500 });
  }
});
