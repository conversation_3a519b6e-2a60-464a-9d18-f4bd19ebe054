import React from 'react';
import { CheckCircle, AlertCircle, AlertTriangle } from 'lucide-react';

interface AccuracyIndicatorProps {
  accuracyLevel: 'high' | 'medium' | 'low';
}

export const AccuracyIndicator: React.FC<AccuracyIndicatorProps> = ({ accuracyLevel }) => {
  const getIndicatorContent = () => {
    switch (accuracyLevel) {
      case 'high':
        return {
          icon: <CheckCircle className="w-4 h-4 text-green-500" />,
          text: 'High accuracy',
          bgColor: 'bg-green-100',
          textColor: 'text-green-700'
        };
      case 'medium':
        return {
          icon: <AlertCircle className="w-4 h-4 text-yellow-500" />,
          text: 'Medium accuracy',
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-700'
        };
      case 'low':
        return {
          icon: <AlertTriangle className="w-4 h-4 text-red-500" />,
          text: 'Low accuracy',
          bgColor: 'bg-red-100',
          textColor: 'text-red-700'
        };
    }
  };

  const content = getIndicatorContent();

  return (
    <div className={`inline-flex items-center px-2 py-1 rounded-full ${content.bgColor}`}>
      {content.icon}
      <span className={`ml-1 text-xs font-medium ${content.textColor}`}>
        {content.text}
      </span>
    </div>
  );
};

export default AccuracyIndicator