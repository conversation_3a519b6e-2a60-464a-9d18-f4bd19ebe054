import React, { useEffect, useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useProfile } from '../../context/ProfileContext';
import { supabase } from '../../lib/supabase';
import { browseProfiles } from '../../lib/profile';

interface ProfileRecord {
  id: string;
  username: string;
  full_name: string | null;
  avatar_url: string | null;
  created_at: string;
  email?: string;
}

export const DetailedUserDebugInfo: React.FC = () => {
  const { user } = useAuth();
  const { profile } = useProfile();
  const [allProfiles, setAllProfiles] = useState<ProfileRecord[]>([]);  const [browseResult, setBrowseResult] = useState<any>(null);
  const [testResult, setTestResult] = useState<string>('');
  const [loading, setLoading] = useState(true);

  const runDirectDatabaseTest = async () => {
    if (!user?.id) return;
    
    try {
      setTestResult('🔄 Testing direct database query...');
      
      // Test 1: Count all profiles
      const { count: totalCount } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });      // Test 2: Get all profiles except current user
      const { data: otherProfiles, error } = await supabase
        .from('profiles')
        .select('id, username, full_name, age, gender, location')
        .neq('id', user.id);
      
      if (error) throw error;
      
      setTestResult(`
✅ Total profiles: ${totalCount}
✅ Other profiles (excluding you): ${otherProfiles?.length || 0}
📋 Other profiles: ${JSON.stringify(otherProfiles, null, 2)}
👥 All profiles should be discoverable (no is_discoverable field needed)
      `);
    } catch (error) {
      setTestResult(`❌ Error: ${error}`);
    }
  };  const fixDiscoverableProfiles = async () => {
    try {
      setTestResult('🔄 Profiles are already discoverable by default (no is_discoverable field needed)...');
      
      // Since we removed the is_discoverable field from the database schema,
      // all profiles are discoverable by default. This is just a placeholder function now.
      
      setTestResult('✅ All profiles are discoverable by default! The is_discoverable field has been removed from the schema.');
    } catch (error) {
      setTestResult(`❌ Error: ${error}`);
    }
  };

  useEffect(() => {
    const fetchDebugData = async () => {
      try {
        // Fetch all profiles directly from database
        const { data, error } = await supabase
          .from('profiles')
          .select('id, username, full_name, avatar_url, created_at')
          .order('created_at', { ascending: false });

        if (error) throw error;
        setAllProfiles(data || []);

        // Test browseProfiles function if user is available
        if (user?.id) {
          console.log('Testing browseProfiles with user ID:', user.id);
          const browseResult = await browseProfiles({}, { page: 1, pageSize: 10 }, user.id);
          console.log('browseProfiles result:', browseResult);
          setBrowseResult(browseResult);
        }
      } catch (error) {
        console.error('Error fetching debug data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDebugData();
  }, [user?.id]);

  if (loading) return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg">
      Loading detailed debug info...
    </div>
  );

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-lg max-h-96 overflow-y-auto z-50">
      <h3 className="font-bold text-lg mb-3 text-gray-800">🔍 Detailed Debug Info</h3>
      
      <div className="mb-4">
        <h4 className="font-semibold text-green-600">Current User:</h4>
        <p className="text-sm">ID: {user?.id}</p>
        <p className="text-sm">Email: {user?.email}</p>
        <p className="text-sm">Profile Name: {profile?.name}</p>
        <p className="text-sm">Username: {profile?.username}</p>
      </div>

      <div className="mb-4">
        <h4 className="font-semibold text-blue-600 mb-2">All Profiles ({allProfiles.length}):</h4>
        {allProfiles.map((p, index) => (
          <div key={p.id} className={`text-xs mb-2 p-2 rounded ${p.id === user?.id ? 'bg-yellow-100 border-2 border-yellow-400' : 'bg-gray-50'}`}>
            <div className="font-medium">#{index + 1} {p.id === user?.id ? '(👤 YOU)' : ''}</div>
            <div>🆔 ID: {p.id}</div>
            <div>👤 Username: {p.username}</div>
            <div>📛 Name: {p.full_name || 'N/A'}</div>
            <div>📅 Created: {new Date(p.created_at).toLocaleString()}</div>
          </div>
        ))}
      </div>      <div className="mb-4">
        <h4 className="font-semibold text-orange-600 mb-2">🧪 Direct Database Test:</h4>        <button 
          onClick={runDirectDatabaseTest}
          className="bg-orange-500 text-white px-3 py-1 rounded text-xs hover:bg-orange-600 mr-2"
        >
          Run Database Test
        </button>        <button 
          onClick={fixDiscoverableProfiles}
          className="bg-green-500 text-white px-3 py-1 rounded text-xs hover:bg-green-600"
        >
          Check Status
        </button>
        {testResult && (
          <div className="mt-2 p-2 bg-orange-50 rounded text-xs whitespace-pre-wrap">
            {testResult}
          </div>
        )}
      </div>      <div className="mb-4">
        <h4 className="font-semibold text-green-600 mb-2">🔧 Profile Discovery Status:</h4>
        <button 
          onClick={fixDiscoverableProfiles}
          className="bg-green-500 text-white px-3 py-1 rounded text-xs hover:bg-green-600"
        >
          Check Discovery Status
        </button>
      </div>

      <div>
        <h4 className="font-semibold text-purple-600 mb-2">🔍 Browse Profiles Result:</h4>
        {browseResult ? (
          <div className="text-xs">
            <div className="mb-2">📊 Total Count: {browseResult.totalCount}</div>
            <div className="mb-2">📋 Profiles Found: {browseResult.profiles?.length || 0}</div>
            {browseResult.profiles?.length > 0 ? (
              browseResult.profiles.map((p: any, index: number) => (
                <div key={p.id} className="mb-1 p-1 bg-purple-50 rounded">
                  <div>#{index + 1}: {p.name} (ID: {p.id})</div>
                </div>
              ))
            ) : (
              <div className="text-red-500 font-semibold">❌ NO PROFILES FOUND BY BROWSE!</div>
            )}
          </div>
        ) : (
          <div className="text-xs text-gray-500">⏳ No browse result yet</div>
        )}
      </div>
    </div>
  );
};
