import React from 'react';
import { User, Heart, MessageCircle, Calendar, Tag, Award } from 'lucide-react';
import { DigitalTwin } from '../../types';

interface TwinProfileProps {
  digitalTwin: DigitalTwin;
  onClose: () => void;
}

const TwinProfile: React.FC<TwinProfileProps> = ({ digitalTwin, onClose }) => {
  return (
    <div className="p-4">
      <div className="mb-6 text-center">
        {digitalTwin.avatar ? (
          <img
            src={digitalTwin.avatar}
            alt={digitalTwin.name}
            className="w-24 h-24 mx-auto rounded-full object-cover mb-4"
          />
        ) : (
          <div className="w-24 h-24 mx-auto rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center text-white text-3xl font-bold mb-4">
            {digitalTwin.name.charAt(0)}
          </div>
        )}        <h1 className="text-2xl font-bold text-gray-800">{digitalTwin.name}{digitalTwin.age ? `, ${digitalTwin.age}` : ''}</h1>
        <p className="text-gray-500 text-sm mt-1">{digitalTwin.gender || ''}</p>
        
        <div className="mt-4 flex justify-center space-x-2">
          <button 
            onClick={onClose}
            className="inline-flex items-center px-4 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition duration-200"
          >
            <MessageCircle className="w-4 h-4 mr-1" />
            Message
          </button>
        </div>
      </div>
        <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-2">About</h2>
        <p className="text-gray-600">{digitalTwin.bio || 'No bio available'}</p>
      </div>
      
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-2 flex items-center">
          <Tag className="w-4 h-4 text-purple-500 mr-2" />
          Personality Traits
        </h2>        <div className="flex flex-wrap gap-2 mt-3">
          {(digitalTwin.personalityTraits || []).map((trait, index) => (
            <div 
              key={trait.id || index}
              className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm flex items-center"
            >
              <span>{trait.name}</span>
              <span className="ml-2 w-4 h-4 bg-white rounded-full flex items-center justify-center text-xs text-purple-600">
                {trait.value || 5}
              </span>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-2 flex items-center">
          <Heart className="w-4 h-4 text-purple-500 mr-2" />
          Interests
        </h2>        <div className="flex flex-wrap gap-2 mt-3">
          {(digitalTwin.interests || []).map((interest, index) => (
            <div 
              key={interest.id || index}
              className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm"
            >
              {interest.name}
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-2 flex items-center">
          <User className="w-4 h-4 text-purple-500 mr-2" />
          Communication Style
        </h2>        <div className="flex flex-wrap gap-2 mt-3">
          {(digitalTwin.communicationStyle || []).map((style: string, index: number) => (
            <div 
              key={index}
              className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm"
            >
              {style}
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-2 flex items-center">
          <Calendar className="w-4 h-4 text-purple-500 mr-2" />
          Relationship Goals
        </h2>        <div className="flex flex-wrap gap-2 mt-3">
          {(digitalTwin.relationshipGoals || []).map((goal: string, index: number) => (
            <div 
              key={index}
              className="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm"
            >
              {goal}
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-2 flex items-center">
          <Award className="w-4 h-4 text-purple-500 mr-2" />
          Compatibility
        </h2>
        <div className="bg-gray-100 rounded-lg p-4">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-gray-600">Compatibility Score</span>
            <span className="text-sm font-semibold text-purple-600">{digitalTwin.compatibilityScore || 85}%</span>
          </div>
          <div className="w-full bg-gray-300 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-purple-600 to-pink-500 h-2 rounded-full" 
              style={{ width: `${digitalTwin.compatibilityScore || 85}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TwinProfile;