export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string
          full_name: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
          age?: number | null
          gender?: string | null
        }
        Insert: {
          id: string
          username: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
          age?: number | null
          gender?: string | null
        }
        Update: {
          id?: string
          username?: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
          age?: number | null
          gender?: string | null
        }
      }
      user_interest_tags: {
        Row: {
          id: string
          tagger_id: string
          tagged_user_id: string
          tag_type: string
          emoji: string | null
          created_at: string
        }
        Insert: {
          id?: string
          tagger_id: string
          tagged_user_id: string
          tag_type: string
          emoji?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          tagger_id?: string
          tagged_user_id?: string
          tag_type?: string
          emoji?: string | null
          created_at?: string
        }
      }
      contact_willingness_status: {
        Row: {
          id: string
          user_id: string
          is_open_to_contact: boolean
          status_message: string | null
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          is_open_to_contact?: boolean
          status_message?: string | null
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          is_open_to_contact?: boolean
          status_message?: string | null
          updated_at?: string
        }
      }
      user_notifications: {
        Row: {
          id: string
          user_id: string
          type: string
          title: string
          message: string
          data: Json | null
          read: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          type: string
          title: string
          message: string
          data?: Json | null
          read?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: string
          title?: string
          message?: string
          data?: Json | null
          read?: boolean
          created_at?: string
        }
      }
      digital_twins: {
        Row: {
          id: string
          owner_id: string
          name: string
          description: string | null
          is_public: boolean
          training_complete: boolean
          model_parameters: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          owner_id: string
          name: string
          description?: string | null
          is_public?: boolean
          training_complete?: boolean
          model_parameters?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          owner_id?: string
          name?: string
          description?: string | null
          is_public?: boolean
          training_complete?: boolean
          model_parameters?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      conversations: {
        Row: {
          id: string
          user_id: string
          twin_id: string | null
          title: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          twin_id?: string | null
          title: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          twin_id?: string | null
          title?: string
          created_at?: string
          updated_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          conversation_id: string
          sender_id: string | null
          twin_id: string | null
          content: string
          created_at: string
        }
        Insert: {
          id?: string
          conversation_id: string
          sender_id?: string | null
          twin_id?: string | null
          content: string
          created_at?: string
        }
        Update: {
          id?: string
          conversation_id?: string
          sender_id?: string | null
          twin_id?: string | null
          content?: string
          created_at?: string
        }
      }
      docs: {
        Row: {
          id: number
          source: string | null
          page: number | null
          chunk_index: number | null
          text: string | null
          embedding: number[] | null
        }
        Insert: {
          id: number
          source?: string | null
          page?: number | null
          chunk_index?: number | null
          text?: string | null
          embedding?: number[] | null
        }
        Update: {
          id?: number
          source?: string | null
          page?: number | null
          chunk_index?: number | null
          text?: string | null
          embedding?: number[] | null
        }
      }
    }
  }
}