import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';

const supabaseUrl = 'https://uggccbkhxpemwutsqsph.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVnZ2NjYmtoeHBlbXd1dHNxc3BoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc4MDQ5MDgsImV4cCI6MjA1MzM4MDkwOH0.Qob2Q2lz48Je97LHHLFwbrNfeKJwL1sh9ZsV4NLhUxc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixRlsPolicies() {
    console.log('🔐 Fixing RLS policies...\n');

    try {
        // Read the SQL file
        const sql = readFileSync('fix_rls_policies_comprehensive.sql', 'utf8');
        
        // Split by semicolons and execute each statement
        const statements = sql.split(';').filter(stmt => stmt.trim() && !stmt.trim().startsWith('--'));
        
        for (const statement of statements) {
            const trimmedStatement = statement.trim();
            if (!trimmedStatement) continue;
            
            console.log(`Executing: ${trimmedStatement.substring(0, 100)}...`);
            
            const { data, error } = await supabase.rpc('exec_sql', { 
                sql_query: trimmedStatement + ';' 
            });
            
            if (error) {
                console.log(`⚠️ Statement result:`, error.message);
            } else {
                console.log(`✅ Statement executed successfully`);
            }
        }
        
        console.log('\n🎉 RLS policy fixes completed!');
        
        // Test the fix by trying to create a tag
        console.log('\n🧪 Testing tag creation...');
        
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
            // Try to create a test tag
            const { data: testTag, error: tagError } = await supabase
                .from('user_interest_tags')
                .insert({
                    tagger_id: user.id,
                    tagged_user_id: user.id, // Self-tag for testing
                    tag_type: 'interested',
                    emoji: '👍'
                })
                .select();
                
            if (tagError) {
                console.error('❌ Tag creation still failing:', tagError);
            } else {
                console.log('✅ Tag creation successful:', testTag);
                
                // Clean up test tag
                await supabase
                    .from('user_interest_tags')
                    .delete()
                    .eq('id', testTag[0].id);
            }
        } else {
            console.log('ℹ️ No authenticated user found for testing');
        }
        
    } catch (error) {
        console.error('❌ Error fixing RLS policies:', error);
    }
}

fixRlsPolicies();
